<template>
  <a-form p24 ref="testFormRef" autocomplete="off" layout="vertical">
    <a-form-item label="考场布置：">
      <div pb16>
        共
        <span class="test-angemt">{{ fullData.studentCount || '-' }}</span>
        位学生，已安排
        <span class="test-angemt">{{
          fullData.scheduledStudentCount || '-'
        }}</span>
        位，共
        <span class="test-angemt">{{
          fullData.monitoringRoomCount || '-'
        }}</span>
        个监控室
      </div>
    </a-form-item>
  </a-form>
  <div p16>
    <ETable
      hash="previewTable"
      :loading="page.loading"
      :showIndex="false"
      :columns="state.tableLabels"
      :dataSource="page.list"
     :total="page.total"
      size="small"
      @paginationChange="paginationChange"
      :current="query.pageNo"
    >
      <template #operation="{ record }">
        <a-button
          type="link"
          class="btn-link-color"
          @click="handerPreview(record)"
          >预览</a-button
        >
      </template>
    </ETable>
  </div>
  <PodiumLayout ref="podiumLayoutRef" />
</template>

<script setup>
import PodiumLayout from '../podiumLayout/index.vue';
const examIdStore = useExamIdStore();
const podiumLayoutRef = ref();

const props = defineProps({
  drawerId: {
    type: [Number, String],
    default: 0,
  },
  drawerDisabled: {
    type: Boolean,
    default: false,
  },
});
const state = reactive({
  fixedObj: {
    id: examIdStore.getExamId || props.drawerId, // 测试管理标识
  },
  tableLabels: [
    {
      title: '学校',
      dataIndex: 'schoolName',
      key: 'schoolName',
    },
    {
      title: '监测室',
      dataIndex: 'monitoringRoomName',
      key: 'monitoringRoomName',
      width: 100,
    },
    // {
    //   title: '监测室号',
    //   dataIndex: 'monitoringRoomNumber',
    //   key: 'monitoringRoomNumber',
    //   width: 50,
    // },
    {
      title: '计算机台数',
      dataIndex: 'computerCount',
      key: 'computerCount',
      width: 50,
    },
    // {
    //   title: '行*列',
    //   dataIndex: 'rowAndCell',
    //   key: 'rowAndCell',
    //   width: 50,
    // },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 50,
    },
  ],
});

let { query, page, getList, paginationChange, fullData } = useList(
  '/manage/examinationSetting/monitoringRoomPreview',
  state.fixedObj,
  {
    dataPath: 'monitoringRoomList',
  }
);

const testFormRef = ref();

// 提交预览
const handSubmitBasic = () => {
  return new Promise((resolve, reject) => {
    http
      .post('/manage/examinationSetting/submit', {
        id: examIdStore.getExamId || props.drawerId,
      })
      .then(res => {
        YMessage.success(res.message);
        resolve(true);
      })
      .catch(error => {
        reject(error);
      });
  });
};

// 预览
const handerPreview = item => {
  const { timeList = [] } = item;
  console.log(item);

  podiumLayoutRef.value.StylePrev.id = examIdStore.getExamId || props.drawerId;
  if (timeList.length) {
    podiumLayoutRef.value.StylePrev.time = timeList[0];
  }
  podiumLayoutRef.value.handerStylePreviewInfo();
  podiumLayoutRef.value.podiumVisible = true;
};

onMounted(() => {
  if (examIdStore.getExamId || props.drawerId) {
    getList();
  }
});

defineExpose({
  handSubmitBasic,
  testFormRef,
});
</script>

<style scoped lang="less"></style>
