<template>
  <div class="LookTestNumber">
    <!-- tabs -->
    <div pb16>
      <a-radio-group @change="changeType" v-model:value="state.infoType" button-style="solid">
        <a-radio-button style="width: 88px; text-align: center" value="theory">理论</a-radio-button>
        <a-radio-button style="width: 88px; text-align: center" value="practicalOperation">实操</a-radio-button>
      </a-radio-group>
    </div>

    <!-- 筛选条件 -->
    <div pb16>
      <searchForm v-model:formState="query" :formList="formList" @submit="searchQuery" @reset="resetBtn" />
      <div pt-16 flex flex-justify-end>
        <a-button @click="allSchoolsDownload">
          <template #icon>
            <i class="iconfont icon-a-xingzhuangjiehe2" mr-4></i>
          </template>
          全部学校下载
        </a-button>
        <a-button ml-12 @click="download"><template #icon>
            <i class="iconfont icon-a-xingzhuangjiehe2" mr-4></i>
          </template>
          下载
        </a-button>
        <a-button @click="printerData">
          <template #icon>
            <PrinterOutlined />
          </template>
          打印
        </a-button>
      </div>
    </div>

    <!-- tabs -->
    <a-tabs class="roster-tabs" v-model:activeKey="testNnumberKey" type="card" @tabClick="handerTabClick">
      <a-tab-pane v-for="item in state.testNnumberItem" :key="item.value" :tab="item.tab" />
    </a-tabs>
    <!-- 座位签，顺序签，条形码的列表 -->
    <a-spin :spinning="state.spinning">
      <SeatBarCode :testType="testNnumberKey" :seatBarCodeData="state.seatBarCodeData"
        :seatBarCodeTotal="state.seatBarCodeTotal" />
    </a-spin>
  </div>

  <PrintSetup :printTitle="state.printTitle" v-model:open="state.printOpen" :options="state.options" :data="testNnumberKey === 'seat' ? state.originalArr : state.originalArr">
    <template #item="{ item }">
      <div class="bar_container__warp" v-if="testNnumberKey === 'seat'">
        <div class="bar_title">{{ item.eventName }}</div>
        <div class="bar_content">
          <span class="bar_content_seat">{{ seats(item.seat) }}</span>
          <ul class="bar_content_abbreviatin">
            <li>{{ item.school }}</li>
            <li>{{ item.examineeNumber }}</li>
          </ul>
        </div>
      </div>

      <div class="bar_container__warp" v-else>
        <div class="item_qrcode_warp">
            <BarCode :id="`barcode_${item.examineeNumber}`" :code="item.examineeNumber" :text="`${item.examineeNumber}`" :title="item.student"/>
            <!-- ${item.student} -->
          </div>
      </div>
    </template>
  </PrintSetup>
</template>

<script setup>
import SeatBarCode from './SeatBarCode.vue';
import Jsbarcode from 'jsbarcode';
// import printJS from 'print-js';
import PrintSetup from './printSetup.vue';
import BarCode from './barCode.vue'

function getPrintOption(type='seat'){
    const print = {
      seat:{
        width: 21,
        height: 29.7,
        row: 3,
        col: 7,
        top: 1.5,
        bottom: 1.6,
        left: 0.5,
        right: 0.5,
        marginR: 0.3,
        marginB: 0
      },
      barCode:{
        width: 21,
        height: 29.7,
        row: 3,
        col: 10,
        top: 0.9,
        bottom: 0.8,
        left: 0.6,
        right: 0.6,
        marginR: 0.3,
        marginB: 0
      }
    }
    return print[type] || {}
}


const route = useRoute();
const state = reactive({
  fixedObj: {
    id: route.query.id,
    type: route.query.fake ? 1 : 2,
  },
  barCodeList: [],
  cityList: [],
  districtList: [],
  schoolList: [],
  monitoringRoom: [], // 监测室
  sessionList: [], // 场次
  subjectList: [], // 年级
  innerSubjectList: [], // 学科
  infoType: 'theory',
  fieldNames: {
    label: 'name',
    value: 'id',
  },
  // tabs
  testNnumberItem: [
    { tab: '座位签', value: 'seat' },
    { tab: '条形码', value: 'barCode' },
  ],
  spinning: false,
  seatBarCodeData: [],
  seatBarCodeTotal: [],
  originalArr: [],
  printSchool: '',
  printOpen: false,
  options:getPrintOption(),
  printTitle:'xxxxxx'
});
const testNnumberKey = ref('seat');
const query = ref({});





// 默认回填第一项筛选条件数据
function defaultData(data) {
  if (data && data.length > 0) {
    query.value.city = data[0].id;
    state.districtList = data[0].children;
    if (state.districtList && state.districtList.length > 0) {
      state.schoolList = state.districtList[0].children;
      query.value.district = state.districtList[0].id;

      if (state.schoolList && state.schoolList.length > 0) {
        query.value.school = state.schoolList[0].id;
        // 理论
        
        if (state.infoType === 'theory') {
          
          state.monitoringRoomList = state.schoolList[0].children;
          if (state.monitoringRoomList && state.monitoringRoomList.length > 0) {
            state.sessionList = state.monitoringRoomList[0].children;
            query.value.monitoringRoom = state.monitoringRoomList[0].id;

            if (state.sessionList && state.sessionList.length > 0) {
              query.value.sessionId = state.sessionList[0].scheduleId;
            }
          }
        } else {
          // 实操
          state.subjectList = state.schoolList[0].children;
          if (state.subjectList && state.subjectList.length > 0) {
            state.innerSubjectList = state.subjectList[0].children;
            query.value.grade = state.subjectList[0].id;

            if (state.innerSubjectList && state.innerSubjectList.length > 0) {
              query.value.subjectIds = state.innerSubjectList[0].subjectIds;
              query.value.subject = state.innerSubjectList[0].id;
            }
          }
        }
      }
    }
  } else {
    state.districtList = [];
    state.schoolList = [];
    state.monitoringRoom = []; // 监测室
    state.sessionList = []; // 场次
    state.subjectList = []; // 年级
  }
}

// 获取省市区
async function getAreaList() {
  let url = '';
  if (route.query.fake) {
    url =
      state.infoType == 'theory'
        ? '/manage/examinationAffairs/pieceUpRoomConditionSimple'
        : '/manage/examinationAffairs/pieceUpScopeConditionSimple';
  } else {
    url =
      state.infoType == 'theory'
        ? '/manage/examinationAffairs/pieceUpRoomCondition'
        : '/manage/examinationAffairs/pieceUpPracticalCondition';
  }
  await http
    .post(url, {
      id: state.fixedObj.id,
    })
    .then(res => {
      if (state.fixedObj.type == 1) {
        state.schoolList = res.data;
      } else {
        state.cityList = res.data;
        defaultData(res.data); // 默认回填第一项筛选数据
      }
    });
}

// 筛选条件
const formList = computed(() => {
  const areaForm = [
    {
      type: 'select',
      value: 'city',
      label: '地市名称',
      list: state.cityList,
      attrs: {
        fieldNames: state.fieldNames,
        onChange: (val, obj) => {
          state.districtList = obj.children;
          query.value.city = val;
          query.value.district = null;
        },
      },
    },
    {
      type: 'select',
      value: 'district',
      label: '县区名称',
      list: state.districtList,
      attrs: {
        fieldNames: state.fieldNames,
        onChange: (val, obj) => {
          state.schoolList = obj.children;
          query.value.district = val;
          query.value.school = null;
        },
      },
    },
  ];
  const schoolForm = [
    {
      type: 'select',
      value: 'school',
      label: '学校名称',
      list: state.schoolList,
      attrs: {
        fieldNames: state.fieldNames,
        onChange: (val, obj) => {
          query.value.school = val;
          if (state.infoType === 'theory') {
            state.monitoringRoomList = obj.children;
            query.value.monitoringRoom = null;
          } else {
            state.subjectList = obj.children;
            query.value.grade = null;
          }
        },
      },
    },
  ];
  const fixedForm =
    state.fixedObj.type == 1 ? schoolForm : [...areaForm, ...schoolForm];
  // 理论筛选条件
  const theoryForm = [
    {
      type: 'select',
      value: 'monitoringRoom',
      label: '监测室号',
      list: state.monitoringRoomList,
      attrs: {
        fieldNames: state.fieldNames,
        onChange: (val, obj) => {
          state.sessionList = obj.children;
          query.value.monitoringRoom = val;
          query.value.sessionId = null;
        },
      },
    },
    {
      type: 'select',
      value: 'sessionId',
      label: '场次',
      list: state.sessionList,
      attrs: {
        fieldNames: {
          label: 'scheduleTime',
          value: 'scheduleId',
        },
        onChange: val => {
          query.value.sessionId = val;
        },
      },
    },
  ];
  // 实操筛选条件
  const practicalOperationForm = [
    {
      type: 'select',
      value: 'grade',
      label: '年级',
      list: state.subjectList,
      attrs: {
        fieldNames: state.fieldNames,
        onChange: (val, obj) => {
          state.innerSubjectList = obj.children;
          query.value.grade = val;
          query.value.innerSubject = null;
        },
      },
    },
    {
      type: 'select',
      value: 'subject',
      label: '实操学科',
      list: state.innerSubjectList,
      attrs: {
        fieldNames: state.fieldNames,
        onChange: (val, obj) => {
          query.value.subjectIds = obj.subjectIds || '';
          query.value.subject = val || '';
        },
      },
    },
  ];
  if (state.infoType === 'theory') {
    return [...fixedForm, ...theoryForm];
  } else {
    return [...fixedForm, ...practicalOperationForm];
  }
});

//理论座位条码获取座位签:seat，条形码信息:barCode
function theoryBarcodeSeatInfo() {
  const prarms = {
    ...state.fixedObj,
    ...query.value,
  };  
  const url =
    testNnumberKey.value == 'seat'
      ? '/manage/examinationAffairs/checkSeatInformation'
      : '/manage/examinationAffairs/checkBarcodeInformation';
  http
    .post(url, prarms)
    .then(res => {
      const { cell, seatList, school } = res.data || {};
      state.spinning = true;
      state.seatBarCodeData = [];
      let seatItem = [];
      // 按例 重装数据
      seatList?.forEach((v, idx) => {
        const num = idx + 1;
        v.idx = num > 9 ? num : `0${num}`;
        if (seatItem.length === 8) {
          state.seatBarCodeData.push({ seatItem });
          seatItem = [];
        }
        seatItem.push(v);
      });
      // 如果循环到最后小于cell时 则执行
      if (seatItem.length <= 8) {
        state.seatBarCodeData.push({ seatItem });
      }
      state.printSchool = school;

      state.seatBarCodeTotal = seatList;
      state.originalArr = seatList;
    })
    .finally(() => {
      state.spinning = false;
    });
}

// 实操座位条码获取顺序签:seat，条形码信息:barCode
function practicalBarcodeSeatInfo() {
  const prarms = {
    ...state.fixedObj,
    ...query.value,
  };
  
  const url =
    testNnumberKey.value == 'seat'
      ? '/manage/examinationAffairs/checkSequenceInformation'
      : '/manage/examinationAffairs/checkPracticalBarcodeInformation';
  http
    .post(url, prarms)
    .then(res => {
      const { sequenceList, barcodeList, school } = res.data || {};
      const cell = 8;
      const arrList =
        testNnumberKey.value === 'seat' ? sequenceList : barcodeList;

      state.spinning = true;
      state.seatBarCodeData = [];
      let seatItem = [];
      // 按例 重装数据
      arrList?.forEach((v, idx) => {
        v.seat = v.sequence;
        const num = idx + 1;
        v.idx = num > 9 ? num : `0${num}`;
        if (seatItem.length === cell) {
          state.seatBarCodeData.push({ seatItem });
          seatItem = [];
        }
        seatItem.push(v);
      });
      // 如果循环到最后小于cell时 则执行
      if (seatItem.length <= cell) {
        state.seatBarCodeData.push({ seatItem });
      }
      state.printSchool = school;
      state.seatBarCodeTotal = arrList;
    })
    .finally(() => {
      state.spinning = false;
    });
}

// 获取座位条码列表
function getBarcodeSeatList() {
  state.seatBarCodeTotal = [];
  state.seatBarCodeData = [];
  switch (state.infoType) {
    case 'theory':
      state.testNnumberItem[0].tab = '座位签';
      theoryBarcodeSeatInfo();
      break;
    case 'practicalOperation':
      state.testNnumberItem[0].tab = '顺序签';
      practicalBarcodeSeatInfo();
      break;
    default:
      break;
  }
}

// 切换理论和实操
const changeType = async () => {
  query.value = {};
  // 获取查询筛选条件数据
  await getAreaList();
  getBarcodeSeatList();
};

// 动态查询
const searchQuery = () => {
  getBarcodeSeatList();
};

// 全部学校下载
function allSchoolsDownload() {
  let allurl = '';
  let allname = '';
  // 如果是全校座位签下载
  if (testNnumberKey.value === 'seat') {
    allurl =
      state.infoType === 'theory'
        ? '/manage/examinationAffairs/downloadAllSeatInformation'
        : '/manage/examinationAffairs/downloadAllSequenceInformation';
    allname =
      state.infoType === 'theory' ? '(理论)全校座位签' : '(实操)全校座位签';
  } else {
    // 如果是全校条形码下载
    allurl =
      state.infoType === 'theory'
        ? '/manage/examinationAffairs/downloadAllBarcodeInformation'
        : '/manage/examinationAffairs/downloadAllPracticalBarcodeInformation';
    allname =
      state.infoType === 'theory' ? '(理论)全校条形码' : '(实操)全校条形码';
  }
  http.download(
    allurl,
    {
      ...state.fixedObj,
      ...query.value,
    },
    allname
  );
}

// 下载
function download() {
  let url = '';
  let name = '';
  // 如果是座位签下载
  if (testNnumberKey.value === 'seat') {
    url =
      state.infoType === 'theory'
        ? '/manage/examinationAffairs/downloadSeatInformation'
        : '/manage/examinationAffairs/downloadSequenceInformation';
    name = state.infoType === 'theory' ? '(理论)座位签' : '(实操)座位签';
  } else {
    // 如果是条形码下载
    url =
      state.infoType === 'theory'
        ? '/manage/examinationAffairs/downloadBarcodeInformation'
        : '/manage/examinationAffairs/downloadPracticalBarcodeInformation';
    name = state.infoType === 'theory' ? '(理论)条形码' : '(实操)条形码';
  }
  http.download(
    url,
    {
      ...state.fixedObj,
      ...query.value,
    },
    name
  );
}

const handerTabClick = event => {
  testNnumberKey.value = event;
  getBarcodeSeatList();
};

onMounted(async () => {
  await getAreaList();
  getBarcodeSeatList();
});

const resetBtn = () => {
  getBarcodeSeatList();
};

const JsbarcodeInfo = (element, data) => {
  Jsbarcode(`#${element}`, String(data.examineeNumber), {
    format: 'CODE39', // auto CODE39  CODE128 选择要使用的条形码类型
    width: 0.7, // 设置条之间的宽度
    // width: 0.7, // 设置条之间的宽度
    height: 62, // 高度
    displayValue: true, // 是否在条形码下方显示文字
    text: `${data.student} ${data.examineeNumber}`, // 覆盖显示的文本
    textAlign: 'left', // 设置文本的水平对齐方式
    textPosition: 'bottom', // 设置文本的垂直位置
    textMargin: 5, // 设置条形码和文本之间的间距
    fontSize: 14, // 设置文本的大小
    // fontOptions: 'bold italic', // 使文字加粗体或变斜体
    // font: 'fantasy', // 设置文本的字体
    // background: '#eee', // 设置条形码的背景
    // lineColor: '#2196f3', // 设置条和文本的颜色。
    margin: 10, // 设置条形码周围的空白边距
  });
};

watch(
  () => state.seatBarCodeTotal,
  val => {
    if (testNnumberKey.value === 'barCode' && val.length) {
      nextTick(() => {
        state.barCodeList = [];
        state.barCodeList = state.seatBarCodeTotal;        
      });
    }
  },
  {
    immediate: true,
  }
);

// 编号
const seats = computed(() => {
  return seat => {
    if (seat) {
      return seat * 1 > 9 ? seat : `0${seat}`;
    }
    return 0;
  };
});

// 调用系统打印
const printerData = () => {
  state.options = getPrintOption(testNnumberKey.value)
  state.printOpen = true    

  const school = state.schoolList.find(i=>i.id==query.value.school)?.name
  state.printTitle = school 
};
</script>

<style lang="less" scoped>
.LookTestNumber {
  .roster-tabs {
    :deep(.ant-tabs-nav) {
      border: none;

      .ant-tabs-tab {
        border-radius: 0;
        background: #ffffff;
        border-color: transparent;
        border-bottom-color: #f0f0f0;
        border-top: 2px solid transparent;
      }

      .ant-tabs-tab-active {
        border: 1px solid #f0f0f0;
        border-top-width: 2px;
        border-top-color: #00b781;
        border-bottom-color: transparent;
      }
    }
  }
}

#a4Paper {
  position: relative;
  height: 100%;
}

.seat-a4-paper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.barCode-a4-paper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

.seat-item_code {
  text-align: center;
}

.a4-paper {
  page-break-after: always;
  /* 确保分页打印 */
  -webkit-print-color-adjust: exact;
  /* 强制打印背景色 */
}

/* 打印样式 */
@media print {

  /* 移除页面边距 */
  @page {
    margin: 0;
    // size: A4; /* 确保页面尺寸为A4 */
  }

  /* 移除HTML和BODY的默认边距 */
  html,
  body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }

  /* 显示并设置A4尺寸的div */
}

.seat-list {
  display: flex;
  flex: 1;
  margin: 0 24px;
}

.seat-item {
  width: 280px;
  min-width: 280px;

  margin-bottom: 8px;
  margin-right: 12px;
  overflow: hidden;
  font-family: FZKTJW--GB1-0, FZKTJW--GB1;
  font-weight: normal;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

// 座位签
.seat-item_bar {
  .bar_content {
    display: flex;
    justify-content: baseline;
  }

  .bar_content_seat {
    width: 60px;
    font-size: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-right: 1px solid #d9d9d9;
  }

  .bar_content_abbreviatin {
    flex: 1;
    padding: 0;

    li {
      padding: 8px;
      font-size: 16px;

      &:not(:last-child) {
        border-bottom: 1px solid #d9d9d9;
      }
    }
  }
}



.bar_title {
  height: 34px;
  line-height: 34px;
  font-size: 16px;
  border-bottom: 1px solid #d9d9d9;
  text-align: center;
  background-color: #7e7e7e;
  color: #ffffff;
}

.bar_container__warp {
  // border: 1px solid #d9d9d9;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;

  .bar_title {
    height: 34px;
    line-height: 34px;
    font-size: 16px;
    border-bottom: 1px solid #d9d9d9;
    text-align: center;
    background-color: #7e7e7e;
    color: #ffffff;
  }

  .bar_content {
    display: flex;
    // justify-content: baseline;
    height: 100%;
  }

  .bar_content_seat {
    width: 60px;
    font-size: 24px;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-right: 1px solid #d9d9d9;
  }

  .bar_content_abbreviatin {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    li {
      padding: 0 8px;
      font-size: 16px;
      flex: 1;
      display: flex;
      align-items: center;

      &:not(:last-child) {
        border-bottom: 1px solid #d9d9d9;
      }
    }
  }

  .item_qrcode_warp{
    width: 100%;
    min-width: 100%;
    text-align: center;
  }
}
</style>
