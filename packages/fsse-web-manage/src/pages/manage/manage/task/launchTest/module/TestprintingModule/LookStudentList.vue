<!-- 查看学生名单 -->
<template>
  <div class="LookStudentList">
    <div class="monitor-change" pb16>
      <a-radio-group
        @change="changeType"
        v-model:value="state.fixedObj.practicalOperation"
        button-style="solid"
      >
        <a-radio-button :value="false">理论</a-radio-button>
        <a-radio-button :value="true">实操</a-radio-button>
      </a-radio-group>
    </div>
    <div class="monitor-info" pb16>
      <!-- 查询条件 -->
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="searchQuery"
        @reset="resetBtn"
      />
    </div>
    <div pb16 class="export-btn">
      <a-button @click="exportData">
        <template #icon>
          <DownloadOutlined />
        </template>
        下载
      </a-button>
    </div>
    <div class="ListTable">
      <ETable
        hash="LookStudentList-table"
        :loading="page.loading"
        :columns="columns"
        :dataSource="page.list"
        :total="page.total"
        @paginationChange="paginationChange"
        :current="query.pageNo"
      >
      </ETable>
    </div>
  </div>
</template>

<script setup name="LookStudentList">
import { computed } from 'vue';

const route = useRoute();
const state = reactive({
  fixedObj: {
    id: route.query.id,
    practicalOperation: false,
    type: route.query.fake ? 1 : 2,
  },
  fieldNames: {
    label: 'name',
    value: 'id',
  },
  cityList: [],
  districtList: [],
  schoolList: [],
});

let { query, page, getList, reset, paginationChange } = useList(
  '/manage/examinationAffairs/checkStudentList',
  state.fixedObj
);

// 获取省市区
async function getAreaList() {
  let url = '';
  if (route.query.fake) {
    url = state.fixedObj.practicalOperation
      ? '/manage/examinationAffairs/pieceUpRoomConditionSimple'
      : '/manage/examinationAffairs/pieceUpScopeConditionSimple';
  } else {
    url = state.fixedObj.practicalOperation
    ? '/manage/examinationAffairs/pieceUpPracticalCondition'
    : '/manage/examinationAffairs/pieceUpRoomCondition';
  }
  await http
    .post(url, {
      id: state.fixedObj.id,
    })
    .then(res => {
      if (state.fixedObj.type == 1) {
        state.schoolList = res.data;
      } else {
        state.cityList = res.data;
      }
    });
}

const formList = computed(() => {
  return state.fixedObj.type == 1
    ? [
        {
          type: 'select',
          value: 'school',
          label: '学校名称',
          list: state.schoolList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: val => {
              query.school = val;
            },
          },
        },
      ]
    : [
        {
          type: 'select',
          value: 'city',
          label: '地市名称',
          list: state.cityList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: (val, obj) => {
              state.districtList = obj.children;
              query.city = val;
              query.district = null;
            },
          },
        },
        {
          type: 'select',
          value: 'district',
          label: '县区名称',
          list: state.districtList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: (val, obj) => {
              state.schoolList = obj.children;
              query.district = val;
              query.school = null;
            },
          },
        },
        {
          type: 'select',
          value: 'school',
          label: '学校名称',
          list: state.schoolList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: val => {
              query.school = val;
            },
          },
        },
      ];
});

const areaColumns = ref([
  {
    title: '地市名称',
    dataIndex: 'city',
    key: 'city',
  },
  {
    title: '县区名称',
    dataIndex: 'district',

    key: 'district',
  },
]);

const columns = computed(() => {
  const defColumns = [
    {
      title: '学校',
      dataIndex: 'school',
      key: 'school',
    },
    {
      title: '学生姓名',
      dataIndex: 'student',
      key: 'student',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
    },
    {
      title: '监测室',
      dataIndex: 'monitoringRoom',
      key: 'monitoringRoom',
    },
    {
      title: '考号',
      dataIndex: 'examineeNumber',
      key: 'examineeNumber',
    },
    {
      title: '所在年级',
      dataIndex: 'grade',
      key: 'grade',
    },
    // {
    //   title: '年龄（岁）',
    //   dataIndex: 'age',
    //   key: 'age',
    // },
    {
      title: '学籍号',
      dataIndex: 'rollNumber',
      key: 'rollNumber',
    },
  ];
  return state.fixedObj.type == 1
    ? defColumns
    : [...areaColumns.value, ...defColumns];
});

const changeType = () => {
  getAreaList();
  reset(state.fixedObj);
};

const searchQuery = () => {
  getList(state.fixedObj);
};
const resetBtn = () => {
  reset(state.fixedObj);
};

const exportData = () => {
  http
    .download(
      '/manage/examinationAffairs/downloadStudentList',
      {
        ...state.fixedObj,
        ...query,
      },
      '查看学生名单'
    )
    .then(res => {
      console.log(res, '导出数据');
    });
};


onMounted(() => {
  getAreaList();
  getList();
});
</script>

<style lang="less" scoped>
.export-btn {
  display: flex;
  justify-content: flex-end;
}
</style>
