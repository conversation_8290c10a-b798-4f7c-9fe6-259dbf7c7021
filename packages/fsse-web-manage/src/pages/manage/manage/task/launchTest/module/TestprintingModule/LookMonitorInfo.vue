<!-- 查看监测点信息 -->
<template>
  <div class="LookMonitorInfo">
    <div class="monitor-change" pb16>
      <a-radio-group
        @change="changeType"
        v-model:value="state.fixedObj.practicalOperation"
        button-style="solid"
      >
        <a-radio-button :value="false">理论</a-radio-button>
        <a-radio-button :value="true">实操</a-radio-button>
      </a-radio-group>
    </div>
    <div class="monitor-info" pb16>
      <!-- 查询条件 -->
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="searchQuery"
        @reset="resetBtn"
      />
    </div>
    <div pb16 class="export-btn">
      <a-button @click="exportData">
        <template #icon>
          <DownloadOutlined />
        </template>
        下载
      </a-button>
    </div>
    <div class="ListTable">
      <ETable
        :scroll="{ x: 1500 }"
        hash="LookMonitorInfo-table"
        :loading="page.loading"
        :columns="columnsCom"
        :dataSource="page.list"
        :total="page.total"
        @paginationChange="paginationChange"
        :current="query.pageNo"
      >
      </ETable>
    </div>
  </div>
</template>

<script setup name="LookMonitorInfo">
import { computed } from 'vue';

const route = useRoute();
const state = reactive({
  fixedObj: {
    practicalOperation: false,
    type: route.query.fake ? 1 : 2, // 1：预测，2：正式
    id: route.query.id,
  },
  fieldNames: {
    label: 'name',
    value: 'id',
  },
  cityList: [],
  districtList: [],
  schoolList: [],
});
let { query, page, getList, reset, paginationChange } = useList(
  '/manage/examinationAffairs/checkMonitoringSiteInformation',
  state.fixedObj
);

const formList = computed(() => {
  return state.fixedObj.type == 1
    ? [
        {
          type: 'select',
          value: 'school',
          label: '学校名称',
          list: state.schoolList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: val => {
              query.school = val;
            },
          },
        },
      ]
    : [
        {
          type: 'select',
          value: 'city',
          label: '地市名称',
          list: state.cityList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: (val, obj) => {
              state.districtList = obj.children;
              query.city = val;
              query.district = null;
            },
          },
        },
        {
          type: 'select',
          value: 'district',
          label: '县区名称',
          list: state.districtList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: (val, obj) => {
              state.schoolList = obj.children;
              query.district = val;
              query.school = null;
            },
          },
        },
        {
          type: 'select',
          value: 'school',
          label: '学校名称',
          list: state.schoolList,
          attrs: {
            fieldNames: state.fieldNames,
            onChange: val => {
              query.school = val;
            },
          },
        },
      ];
});

// 预测不要这个省市区
const areaColumns = ref([
  {
    title: '地市名称',
    dataIndex: 'city',
    key: 'city',
    fixed: 'left',
    width: 150,
  },
  {
    title: '县区名称',
    dataIndex: 'district',
    key: 'district',
    fixed: 'left',
    width: 150,
  },
]);
// 预测和正式的理论表格
const columns = computed(() => {
  const defColumns = [
    {
      title: '学校',
      dataIndex: 'school',
      key: 'school',
      width: 150,
    },
    {
      title: '监测教室',
      dataIndex: 'room',
      key: 'room',
      width: 150,
    },
    // {
    //   title: '行*列',
    //   dataIndex: 'rowAndCell',
    //   key: 'rowAndCell',
    //   width: 150,
    // },
    {
      title: '主监测员',
      dataIndex: 'mainInvigilator',
      key: 'mainInvigilator',
    },
    // {
    //   title: '监测年级',
    //   dataIndex: 'mainGrade',
    //   key: 'mainGrade',
    // },
    {
      title: '主监测员所属学校',
      dataIndex: 'mainInvigilatorSchool',
      key: 'mainInvigilatorSchool',
    },
    {
      title: '副监测员',
      dataIndex: 'secondryInvigilator',
      key: 'secondryInvigilator',
    },
    // {
    //   title: '监测年级',
    //   dataIndex: 'secondryGrade',
    //   key: 'secondryGrade',
    // },
    // {
    //   title: '计算机技术人员',
    //   dataIndex: 'computer',
    //   key: 'computer',
    // },
    // {
    //   title: '监测点主任',
    //   dataIndex: 'director',
    //   key: 'director',
    // },
    // {
    //   title: '监测点副主任',
    //   dataIndex: 'secondryDirector',
    //   key: 'secondryDirector',
    // },
    {
      title: '信息员',
      dataIndex: 'info',
      key: 'info',
    },
    {
      title: '学校负责人',
      dataIndex: 'schoolPrincipal',
      key: 'schoolPrincipal',
    }
    // {
    //   title: '安保人员',
    //   dataIndex: 'securitier',
    //   key: 'securitier',
    // },
    // {
    //   title: '司时员',
    //   dataIndex: 'timekeeper',
    //   key: 'timekeeper',
    // },
  ];
  if (state.fixedObj.type == 1) {
    return defColumns;
  } else {
    return [...areaColumns.value, ...defColumns];
  }
});

// 预测和正式的实操表格
const shicaoColumns = computed(() => {
  const defColumns = [
    {
      title: '学校',
      dataIndex: 'school',
      key: 'school',
    },
    {
      title: '监测学科',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: '监测年级',
      dataIndex: 'mainGrade',
      key: 'mainGrade',
    },
    {
      title: '主监测员',
      dataIndex: 'mainInvigilator',
      key: 'mainInvigilator',
    },
    {
      title: '主监测员所属学校',
      dataIndex: 'mainInvigilatorSchool',
      key: 'mainInvigilatorSchool',
    },
    {
      title: '副监测员',
      dataIndex: 'secondryInvigilator',
      key: 'secondryInvigilator',
    },
    // {
    //   title: '设备技术人员',
    //   dataIndex: 'deviceTechnician',
    //   key: 'deviceTechnician',
    // },
  ];
  if (state.fixedObj.type == 1) {
    return defColumns;
  } else {
    return [...areaColumns.value, ...defColumns];
  }
});

const columnsCom = computed(() => {
  return state.fixedObj.practicalOperation
    ? shicaoColumns.value
    : columns.value;
});

// 获取省市区
async function getAreaList() {
  let url = '';
  if (state.fixedObj.type == 1) {
    // 预测
    url = state.fixedObj.practicalOperation
      ? '/manage/examinationAffairs/pieceUpRoomConditionSimple'
      : '/manage/examinationAffairs/pieceUpScopeConditionSimple';
  } else {
    // 正式
    url = state.fixedObj.practicalOperation
      ? '/manage/examinationAffairs/pieceUpPracticalCondition'
      : '/manage/examinationAffairs/pieceUpRoomCondition';
  }
  await http
    .post(url, {
      id: state.fixedObj.id,
    })
    .then(res => {
      if (state.fixedObj.type == 1) {
        state.schoolList = res.data;
      } else {
        state.cityList = res.data;
      }
    });
}

const changeType = () => {
  getAreaList();
  reset(state.fixedObj);
};
const searchQuery = () => {
  getList(state.fixedObj);
};
const resetBtn = () => {
  reset(state.fixedObj);
};

const exportData = () => {
  http
    .download(
      '/manage/examinationAffairs/downloadMonitoringSiteInformation',
      {
        ...state.fixedObj,
        ...query,
      },
      '查看监测点信息'
    )
    .then(res => {
      console.log(res, '导出数据');
    });
};

onMounted(() => {
  getAreaList();
  getList(state.fixedObj);
});
</script>

<style lang="less" scoped>
.export-btn {
  display: flex;
  justify-content: flex-end;
}
</style>
