<template>
  <div class="questionSetPage">
    <div class="addBtn">
      <a-button type="primary" @click="setQuestionnaire">
        <template #icon>
          <i mr-4 class="iconfont icon-a-xingzhuangjiehe12"></i>
        </template>
        设置问卷
      </a-button>
      <a-button @click="reqAllPackDowm">
        <template #icon>
          <i mr-4 class="iconfont icon-a-xingzhuangjiehe2"></i>
        </template>
        打包下载
      </a-button>
    </div>
    <div>
      <div class="no_dataExam" v-if="state.questionnaireList.length === 0">
        <img
          width="180"
          height="180"
          src="@/assets/images/empty.png"
          alt="请设置问卷"
        />
        <p>请设置问卷</p>
      </div>
      <div class="examContent" v-else>
        <a-row :gutter="16">
          <a-col
            :span="12"
            v-for="(item, index) in state.questionnaireList"
            :key="index"
          >
            <div class="examInfoBox">
              <div class="stuBox">
                <div class="examTitle">
                  <span>{{ item.name }}</span>
                  <a-tag
                    ml-8
                    :class="
                      item.status == '进行中'
                        ? 'bg1'
                        : item.status == '未开始'
                          ? 'bg2'
                          : item.status == '已结束'
                            ? 'bg3'
                            : ''
                    "
                    >{{ item.status }}</a-tag
                  >
                </div>
              </div>
              <div class="text">
                填卷起止时间：<span style="padding-right: 24px" class="timeText"
                  >{{ dayjs(item.startTime).format('YYYY/MM/DD') }} -
                  {{ dayjs(item.endTime).format('YYYY/MM/DD') }}</span
                >
              </div>
              <div class="text">
                共计：<span class="timeText"
                  >{{ item.count ? item.count : 0 }}份问卷</span
                >
              </div>
              <div class="downBtn">
                <div class="btnlistitem" @click="openlookpaper(item)">
                  <i class="iconfont icon-xiazai-01"></i> 查看问卷
                </div>
                <div @click="reqPackDowm(item)" class="btnlistitem">
                  <i class="iconfont icon-xiazai-01"></i> 打包下载{{
                    item.name
                  }}
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>

  <a-modal
    v-model:open="state.visible"
    :keyboard="false"
    :maskClosable="false"
    title="设置问卷"
    cancelText="取消"
    okText="确认"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="570"
    :bodyStyle="{ overflowY: 'auto', maxHeight: '600px', padding: '24px' }"
  >
    <a-form
      name="timeset"
      autocomplete="off"
      :model="formState"
      ref="formRef"
      layout="vertical"
    >
      <a-form-item
        label="问卷发放形式："
        mb24
        :rules="[{ required: true, message: '请选择问卷发放形式' }]"
      >
        <a-select
          v-model:value="state.grandWay"
          :disabled="state.radioDisabled"
        >
          <a-select-option :value="0"
            >二维码与链接方式（不同学校生成不同二维码与链接）</a-select-option
          >
          <a-select-option :value="1">短信形式（隐匿记名方式）</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="校长问卷：" mb24 required>
        <div class="timesBox">
          <div class="timesBoxitem">
            <a-form-item
              name="masterStartTime"
              :rules="[{ required: true, message: '请选择开始时间' }]"
            >
              <a-date-picker
                v-model:value="formState.masterStartTime"
                placeholder="开始时间"
                format="YYYY-MM-DD"
                value-format="x"
                :disabled-date="disabledStartDate"
                @change="handlePrincipalStartChange"
                :disabled="state.disabled"
              />
            </a-form-item>
          </div>
          <div class="boxTitle">至</div>
          <div class="timesBoxitem">
            <a-form-item
              name="masterEndTime"
              :rules="[{ required: true, message: '请选择结束时间' }]"
            >
              <a-date-picker
                v-model:value="formState.masterEndTime"
                placeholder="结束时间"
                format="YYYY-MM-DD"
                value-format="x"
                :disabled-date="
                  date => disabledEndDate(date, formState.masterStartTime)
                "
              />
            </a-form-item>
          </div>
        </div>
      </a-form-item>

      <a-form-item
        label="选择问卷："
        name="masterPaperGroupId"
        mb24
        :rules="[{ required: true, message: '请选择问卷' }]"
      >
        <a-select
          placeholder="选择问卷"
          v-model:value="formState.masterPaperGroupId"
          :disabled="state.radioDisabled"
          :options="state.masterArr"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
        >
        </a-select>
      </a-form-item>

      <div class="linebox"></div>

      <a-form-item label="教师问卷：" mb24 required>
        <div class="timesBox">
          <div class="timesBoxitem">
            <a-form-item
              name="teacherStartTime"
              :rules="[{ required: true, message: '请选择开始时间' }]"
            >
              <a-date-picker
                :disabled="state.disabled1"
                v-model:value="formState.teacherStartTime"
                placeholder="开始时间"
                format="YYYY-MM-DD"
                value-format="x"
                :disabled-date="disabledStartDate"
                @change="handleTeacherStartChange"
              />
            </a-form-item>
          </div>
          <div class="boxTitle">至</div>
          <div class="timesBoxitem">
            <a-form-item
              name="teacherEndTime"
              :rules="[{ required: true, message: '请选择结束时间' }]"
            >
              <a-date-picker
                v-model:value="formState.teacherEndTime"
                style="width: 100%"
                placeholder="结束时间"
                format="YYYY-MM-DD"
                value-format="x"
                :disabled-date="
                  date => disabledEndDate(date, formState.teacherStartTime)
                "
              />
            </a-form-item>
          </div>
        </div>
      </a-form-item>

      <a-form-item
        name="teacherPaperGroupId"
        label="选择问卷："
        mb24
        :rules="[{ required: true, message: '请选择问卷' }]"
      >
        <a-select
          ref="select"
          v-model:value="formState.teacherPaperGroupId"
          :disabled="state.radioDisabled"
          :options="state.teacherArr"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          placeholder="选择问卷"
        >
        </a-select>
      </a-form-item>

      <div class="linebox"></div>

      <!-- <a-form-item label="家长问卷：" mb24 required>
        <div class="timesBox">
          <div class="timesBoxitem">
            <a-form-item
              name="elternStartTime"
              :rules="[{ required: true, message: '请选择开始时间' }]"
            >
              <a-date-picker
                v-model:value="formState.elternStartTime"
                placeholder="开始时间"
                format="YYYY-MM-DD"
                :disabled="state.disabled2"
                value-format="x"
                :disabled-date="disabledStartDate"
                @change="handleParentStartChange"
              />
            </a-form-item>
          </div>
          <div class="boxTitle">至</div>
          <div class="timesBoxitem">
            <a-form-item
              name="elternEndTime"
              :rules="[{ required: true, message: '请选择结束时间' }]"
            >
              <a-date-picker
                v-model:value="formState.elternEndTime"
                placeholder="结束时间"
                format="YYYY-MM-DD"
                value-format="x"
                :disabled-date="
                  date => disabledEndDate(date, formState.elternStartTime)
                "
              />
            </a-form-item>
          </div>
        </div>
      </a-form-item> -->

      <!-- <a-form-item
        label="家长问卷对象"
        mb24
        name="elternScope"
        :rules="[{ required: true, message: '请选择家长问卷对象' }]"
      >
        <a-select
          v-model:value="formState.elternScope"
          placeholder="请选择家长问卷对象"
        >
          <a-select-option :value="0">监测年级全体家长</a-select-option>
          <a-select-option :value="1">样本学生家长</a-select-option>
        </a-select>
      </a-form-item> -->
<!-- 
      <a-form-item
        label="选择问卷："
        mb24
        name="elternPaperGroupId"
        :rules="[{ required: true, message: '请选择问卷' }]"
      >
        <a-select
          ref="select"
          v-model:value="formState.elternPaperGroupId"
          :disabled="state.radioDisabled"
          :options="state.elternArr"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          placeholder="选择问卷"
        >
        </a-select>
      </a-form-item> -->
    </a-form>
  </a-modal>

  <LookQuestionPaper ref="LookQuestionPaperRef"> </LookQuestionPaper>
</template>

<script setup name="QuestionSet">
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { useRoute } from 'vue-router';

import LookQuestionPaper from './lookQuestionPaper.vue';

dayjs.extend(advancedFormat);
dayjs.extend(isSameOrAfter);
const LookQuestionPaperRef = ref(null);

const route = useRoute();
const state = reactive({
  masterArr: [],
  teacherArr: [],
  elternArr: [],
  quesId: null, // 问卷标识
  monitoringId: route.query.id, // 监测ID
  grandWay: 1,
  radioDisabled: false,
  visible: false,
  questionnaireList: [],
  timeList: [
    {
      endTime: '',
      startTime: '',
      type: 'master',
    },

    {
      endTime: '',
      startTime: '',
      type: 'teacher',
    },
    // {
    //   endTime: '',
    //   startTime: '',
    //   type: 'eltern',
    //   // scope: null,
    // },
  ],
});
const formRef = ref();

const formState = ref({
  masterStartTime: null,
  masterEndTime: null,
  teacherStartTime: null,
  teacherEndTime: null,
  // elternStartTime: null,
  // elternEndTime: null,
});

// 将对象转换为数组 凑请求参数
function objToArr(obj) {
  const types = new Set();
  Object.keys(obj).forEach(key => {
    const match = key.match(/^(.*?)(StartTime|EndTime|Scope|PaperGroupId)$/);
    if (match) types.add(match[1]);
  });
  return Array.from(types).reduce((arr, type) => {
    const startTime = obj[`${type}StartTime`];
    const endTime = obj[`${type}EndTime`];
    const paperGroupId =  obj[`${type}PaperGroupId`];
    if (startTime !== undefined && endTime !== undefined) {
      const item = { paperGroupId, type, startTime, endTime };
      // const scope = obj[`${type}Scope`];
      // if (scope !== undefined) item.scope = scope;
      arr.push(item);
    }
    return arr;
  }, []);
}

// 将数组转换为对象 回显
function arrToObj(arr) {
  return arr.reduce((obj, item) => {
    const type = item.type;
    obj[`${type}StartTime`] = item.startTime;
    obj[`${type}EndTime`] = item.endTime;
    if (item.paperGroupId !== undefined)
      obj[`${type}PaperGroupId`] = item.paperGroupId;
    // if (item.scope !== undefined) obj[`${type}Scope`] = item.scope;
    return obj;
  }, {});
}

// 设置问卷按钮
const setQuestionnaire = () => {
  console.log(state.settingObj, '问卷参数');

  if (state.questionnaireList.length > 0) {
    // 问卷发放形式
    state.grandWay = state.settingObj.grandWay;
    const arrData = JSON.parse(JSON.stringify(state.settingObj.timeList));
    const obj = arrToObj(arrData);
    formState.value = obj;
    console.log(formState.value, '转问卷参数');
  }
  // 编辑状态下置灰时间输入框
  // 当已经到了开始时间，则开始时间不可更改，截止时间可更改
  state.disabled =
    state.questionnaireList.length > 0
      ? dayjs().isSameOrAfter(Number(state.timeList[0].startTime), 'day')
      : false;
  state.disabled1 =
    state.questionnaireList.length > 0
      ? dayjs().isSameOrAfter(Number(state.timeList[1].startTime), 'day')
      : false;
  state.disabled2 =
    state.questionnaireList.length > 0
      ? dayjs().isSameOrAfter(Number(state.timeList[2].startTime), 'day')
      : false;

  // 当其中一个问卷到了开始时间，则问卷发放形式不可更改
  state.radioDisabled =
    !!state.disabled || !!state.disabled1 || !!state.disabled2;
  state.visible = true;
};

// 获取问卷列表
const reqSurveyList = () => {
  http
    .get('/manage/questionnaireManagement/queryByEvaluationId', {
      monitoringId: state.monitoringId,
    })
    .then(res => {
      if (!res.data) {
        return;
      }
      const { questionnaireList, settingObj, id } = res.data;
      // 获取问卷发放形式
      state.settingObj = settingObj;
      state.questionnaireList = questionnaireList;
      // 获取问卷管理标识
      state.quesId = id;
    });
};

const handleOk = () => {
  formRef.value
    .validate()
    .then(() => {
      const timeList = objToArr({
        ...formState.value,
      });

      http
        .post('/manage/questionnaireManagement/basicSetting', {
          id: route.query.id,
          grandWay: state.grandWay, // 发放方式
          timeList: timeList, // 数组
        })
        .then(res => {
          YMessage.success(res.message);
          state.visible = false;
          reqSurveyList();
        });
    })
    .catch(error => {
      console.log('验证失败:', error);
    });
};

// 清除表单验证 关闭弹窗
const handleCancel = () => {
  formRef.value.resetFields();
  state.visible = false;
};

// 禁用开始日期：今天之前的日期不可选
const disabledStartDate = date => {
  // if (!date) return false;
  return date && date < dayjs().subtract(1, 'days');
  // return date.valueOf() < dayjs().startOf('day').valueOf();
};

// 禁用结束日期：开始时间之前的日期不可选（允许选择同一天）
const disabledEndDate = (date, startTime) => {
  if (!date || !startTime) return false;
  // 由于现在使用时间戳，直接比较数值即可
  return date.valueOf() < dayjs(startTime).valueOf();
};

// 开始时间变化时，如果结束时间小于开始时间，则清空结束时间
const handlePrincipalStartChange = () => {
  const { masterEndTime, masterStartTime } = formState.value;
  if (masterEndTime && masterStartTime && masterEndTime < masterStartTime) {
    formState.value.masterEndTime = null;
  }
};

const handleTeacherStartChange = () => {
  const { teacherEndTime, teacherStartTime } = formState.value;
  if (teacherEndTime && teacherStartTime && teacherEndTime < teacherStartTime) {
    formState.value.teacherEndTime = null;
  }
};

const handleParentStartChange = () => {
  const { elternEndTime, elternStartTime } = formState.value;
  if (elternEndTime && elternStartTime && elternEndTime < elternStartTime) {
    formState.value.elternEndTime = null;
  }
};

// 打包下载
const reqPackDowm = item => {
  http.download(
    '/manage/questionnaireManagement/download',
    { id: state.quesId, type: item.type },
    item.name,
    null,
    'application/zip'
  );
};

const openlookpaper = item => {
  LookQuestionPaperRef.value.showModal(item);
};

// 全部下载
const reqAllPackDowm = () => {
  http.download(
    '/manage/questionnaireManagement/download',
    { id: state.quesId },
    '问卷附件',
    null,
    'application/zip'
  );
};

// 直接获取老师校长家长的问卷分组
const masterGetUsablePaperGroup = () => {
  http
    .get('/manage/questionnaireManagement/getUsablePaperGroup', {
      monitoringId: route.query.id,
      identity: 'master',
    })
    .then(res => {
      state.masterArr = res.data;
    });
};
const teacherGetUsablePaperGroup = () => {
  http
    .get('/manage/questionnaireManagement/getUsablePaperGroup', {
      monitoringId: route.query.id,
      identity: 'teacher',
    })
    .then(res => {
      state.teacherArr = res.data;
    });
};
const elternGetUsablePaperGroup = () => {
  http
    .get('/manage/questionnaireManagement/getUsablePaperGroup', {
      monitoringId: route.query.id,
      identity: 'eltern',
    })
    .then(res => {
      state.elternArr = res.data;
    });
};

onMounted(() => {
  masterGetUsablePaperGroup();
  teacherGetUsablePaperGroup();
  elternGetUsablePaperGroup();
  // 获取问卷列表
  reqSurveyList();
});
</script>

<style lang="less" scoped>
.addBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 20px;
  padding-bottom: 20px;
}

.examContent {
  .examInfoBox {
    width: 100%;
    height: 160px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 16px;
    position: relative;
    .text {
      padding: 8px 0px;
      .timeText {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
      }
    }
    .examTitle {
      height: 22px;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
    }
    .stuBox {
      display: flex;
      justify-content: space-between;
      padding-bottom: 16px;
    }

    .downBtn {
      color: var(--primary-color);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .btnlistitem {
        padding-right: 12px;
      }
    }
  }
}
.bg1 {
  color: var(--primary-color);
  border-color: var(--primary-color);
}
.bg2 {
  color: #faad14;
  border-color: #faad14;
}
.bg3 {
  color: rgba(0, 0, 0, 0.45);
  border-color: rgba(0, 0, 0, 0.45);
}

.promptText {
  color: rgba(0, 0, 0, 0.65);
}

.timesBox {
  display: flex;
  .timesBoxitem {
    flex: 1;
  }
  .boxTitle {
    width: 30px;
    text-align: center;
    // padding-left: 8px;
    // padding-right: 8px;
    padding-top: 4px;
  }
}

.no_dataExam {
  text-align: center;
  img {
    display: inline-block;
    margin-top: 150px;
    width: 180px;
  }
  p {
    color: rgba(0, 0, 0, 0.65);
  }
}

.linebox {
  width: 100%;
  height: 1px;
  padding-bottom: 16px;
  border-top: 1px dashed #d9d9d9;
}
</style>
