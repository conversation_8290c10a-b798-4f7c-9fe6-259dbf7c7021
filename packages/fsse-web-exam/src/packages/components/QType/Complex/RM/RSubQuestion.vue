<template>
  <!-- 因为只有考试有材料分析 所以如果是问卷就不可能出现这里的代码 这Rm的代码所有只控制考试的就行了 -->
  <div v-for="(item, index) in config.subQues" :key="item.id">
    <div :style="titleFontStyles" class="title-wrap">
      <div class="ques-type-wrap">
        {{ index + 1 }}.【{{ item.quesTypeAlias }}】
      </div>
      <div class="title" v-html="item.title"></div>
    </div>
    <!-- 如果材料分析的子题里面出现了 填空题 下面的全部都直接不渲染了 -->
    <!-- 考试的填空题全部用笔在答题卡上写 -->
    <div
      class="content"
      v-if="!excludeQuesTypeCode.includes(item.quesTypeCode)"
    >
      <component
        :is="item.config.key"
        :config="item"
        :topProperties="topProperties"
      />
    </div>
    <div
      v-if="excludeQuesTypeCode.includes(item.quesTypeCode)"
      class="answer-area"
    >
      请在纸质答题卡上作答
    </div>
  </div>
</template>

<script setup>
// *********************
// Hooks Function
// *********************

// 排除的题型由组件内部控制题干title字段的渲染 ,比如FA填空题所有内容必须放在自己组件内部去实现逻辑
const excludeQuesTypeCode = ['FA'];

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  topProperties: {
    type: Object,
    default: () => {},
  },
});

const titleFontStyles = computed(() => {
  const styleMap = props.topProperties?.titleFontStyles || {};
  return {
    ...styleMap,
    fontSize: (styleMap?.fontSize || 20) + 'px',
  };
});

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************
</script>

<style lang="less" scoped>
.title-wrap {
  display: flex;
  // align-items: center;
  align-items: start;
  font-weight: 400;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.85);
  margin: 24px 0;
  .title {
    flex: 1;
  }
}
.content {
  margin-left: 23px;
}

.answer-area {
  padding-top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #f5f5f5;
  border-radius: 4px;
  font-weight: 500;
  font-size: 26px;
  color: #d7d7d7;
}
</style>
