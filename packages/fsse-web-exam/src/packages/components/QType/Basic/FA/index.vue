<template>
  <div class="FA">
    <!-- 记住 如果是examination 这种都是纸质答题卡 -->
    <template v-if="getExamDetail.paperCode === 'examination'">
      <div :style="titleFontStyles" class="title-wrap">
        <div class="ques-type-wrap">【{{ config.quesTypeAlias }}】</div>
        <div class="title" v-html="config.title"></div>
      </div>
      <audioPlay :config="config" />
      <div class="answer-area">请在纸质答题卡上作答</div>
    </template>

    <template v-else>
      <div :style="titleFontStyles" class="title-wrap">
        <div class="ques-type-wrap">【{{ config.quesTypeAlias }}】</div>
        <div
          class="title"
          :ref="el => setTitleRef(el)"
          v-html="formatFillingContent(config.title)"
        ></div>
      </div>
      <audioPlay :config="config" />
      <!-- 填空不规范显示错误信息 引导用户进行修改 -->
      <div class="errorMessage" v-show="state.errorMessage">
        <div>
          <i class="iconfont icon-xinxiyouwu" style="color: #f5222d"></i>
        </div>
        <div class="errorMessage_text">
          {{ state.errorMessage }}
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import dayjs from 'dayjs';
const controls = inject('controls');
import { DatePicker, TimePicker } from 'ant-design-vue';
import audioPlay from '@/packages/global/audioPlay.vue';
import { computed, h, nextTick, render, watch, defineComponent } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';

const store = useStore();

// 创建内联可编辑的span组件
const InlineInput = defineComponent({
  name: 'InlineInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入'
    }
  },
  emits: ['update:value', 'change', 'blur'],
  setup(props, { emit }) {
    const handleInput = (e) => {
      const value = e.target.textContent || '';
      emit('update:value', value);
      emit('change', { target: { value } });
    };

    const handleBlur = (e) => {
      const value = e.target.textContent || '';
      emit('blur', { target: { value } });
    };

    const handleKeydown = (e) => {
      // 阻止换行
      if (e.key === 'Enter') {
        e.preventDefault();
        e.target.blur();
      }
    };

    return () => h('span', {
      class: 'inline-question-blank',
      contenteditable: 'true',
      textContent: props.value || '',
      placeholder: props.placeholder,
      onInput: handleInput,
      onBlur: handleBlur,
      onKeydown: handleKeydown,
      style: {
        display: 'inline',
        minWidth: '56px',
        WebkitUserModify: 'read-write-plaintext-only',
        backgroundPositionY: '100%',
        backgroundRepeat: 'no-repeat',
        backgroundSize: '100% 1px',
        wordBreak: 'break-all',
        backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2))',
        padding: '0px 4px',
        margin: '0px 4px',
        outline: 'none'
      }
    });
  }
});
const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  topProperties: {
    type: Object,
    default: () => {},
  },
});

const titleFontStyles = computed(() => {
  const styleMap = props.topProperties?.titleFontStyles || {};
  return {
    ...styleMap,
    fontSize: (styleMap.fontSize || 20) + 'px',
  };
});

const getExamDetail = computed(() => {
  return store.getExamDetail;
});

// 临时收集的答案值
const state = reactive({
  inputValues: {},
  errorMessage: '', // 错误提示
});

// 把title字符串再做一次处理 目的是在标签上就记录好属性,为render转成组件做准备
const formatFillingContent = content => {
  if (!content) return '';
  let index = 0;
  return content.replace(/_{6,}/g, match => {
    index++;
    return `<span class="filling-box" index='${index}' optionNo="${props.config.options[index - 1].optionNo}" faValidation="${props.config.options[index - 1].optionProperties.faValidation}" faMinValue="${props.config.options[index - 1].optionProperties.faMinValue}" faMaxValue="${props.config.options[index - 1].optionProperties.faMaxValue}"  >${match}</span>`;
  });
};

const transformFilling = element => {
  if (!element) return;

  // 获取缓存答案
  const answers = computed(() => store.getAnswers(props.config.quesCode));

  // 答案回显处理
  watch(
    answers,
    newAnswers => {
      if (newAnswers?.length) {
        newAnswers.forEach(answer => {
          state.inputValues[answer.optionNo] = answer.text;
        });
      }
    },
    { immediate: true, deep: true }
  );

  // 统一处理答案变更
  const handleAnswerChange = (key, value) => {
    const mergedArray = (answers.value || [])
      .filter(ans => ans.optionNo !== key)
      .concat(value ? { optionNo: key, text: value } : []);
    controls.collectAnswers(props.config.quesCode, mergedArray);
  };

  // 组件配置映射
  const componentMap = {
    '文本': {
      type: InlineInput,
      props: {
        placeholder: '请输入文本',
      },
    },
    '年月': {
      type: DatePicker,
      props: {
        inputReadOnly: true,
        picker: 'month',
        format: 'YYYY-MM',
        valueFormat: 'YYYY-MM',
        placeholder: '请选择年月',
      },
    },
    '日期': {
      type: DatePicker,
      props: {
        showNow: false,
        inputReadOnly: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择日期',
      },
    },
    '日期与时间': {
      type: DatePicker,
      props: {
        showNow: false,
        inputReadOnly: true,
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择日期与时间',
      },
    },
    '时间': {
      type: TimePicker,
      props: {
        showNow: false,
        hideDisabledOptions: true,
        inputReadOnly: true,
        format: 'HH:mm:ss',
        valueFormat: 'HH:mm:ss',
        placeholder: '请选择时间',
      },
    },
    '整数': {
      type: InlineInput,
      props: {
        placeholder: '请输入整数',
      },
    },
    '小数': {
      type: InlineInput,
      props: {
        placeholder: '请输入小数',
      },
    },
    '手机': {
      type: InlineInput,
      props: {
        placeholder: '请输入手机号',
      },
    },
  };

  // 动态的创建组件
  const createComponent = (inputItem, key) => {
    const validationType = inputItem.getAttribute('faValidation');
    const faMinValue = inputItem.getAttribute('faMinValue');
    const faMaxValue = inputItem.getAttribute('faMaxValue');

    // 处理日期时间限制
    const getDisabledDate = (min, max) => {
      return current => {
        if (!current) return false;

        const currentDate = dayjs(current);
        let isDisabled = false;

        if (min) {
          const minDate = dayjs(min);
          // 使用startOf('day')来比较日期部分，确保边界值可选
          isDisabled =
            isDisabled ||
            currentDate.startOf('day').isBefore(minDate.startOf('day'));
        }

        if (max) {
          const maxDate = dayjs(max);
          // 使用startOf('day')来比较日期部分，确保边界值可选
          isDisabled =
            isDisabled ||
            currentDate.startOf('day').isAfter(maxDate.startOf('day'));
        }

        return isDisabled;
      };
    };

    const getDisabledTime = (min, max) => {
      return () => {
        const minTime = min ? dayjs(`2000-01-01 ${min}`) : null;
        const maxTime = max ? dayjs(`2000-01-01 ${max}`) : null;

        const disabledHours = () => {
          const hours = [];
          if (minTime) {
            // 禁用小于最小时间的小时
            for (let i = 0; i < minTime.hour(); i++) {
              hours.push(i);
            }
          }
          if (maxTime) {
            // 禁用大于最大时间的小时
            for (let i = maxTime.hour() + 1; i < 24; i++) {
              hours.push(i);
            }
          }
          return hours;
        };

        const disabledMinutes = selectedHour => {
          const minutes = [];
          if (minTime && selectedHour === minTime.hour()) {
            // 在最小时间的小时内，禁用小于最小时间的分钟
            for (let i = 0; i < minTime.minute(); i++) {
              minutes.push(i);
            }
          }
          if (maxTime && selectedHour === maxTime.hour()) {
            // 在最大时间的小时内，禁用大于最大时间的分钟
            for (let i = maxTime.minute() + 1; i < 60; i++) {
              minutes.push(i);
            }
          }
          return minutes;
        };

        const disabledSeconds = (selectedHour, selectedMinute) => {
          const seconds = [];
          if (
            minTime &&
            selectedHour === minTime.hour() &&
            selectedMinute === minTime.minute()
          ) {
            // 在最小时间的小时和分钟内，禁用小于最小时间的秒
            for (let i = 0; i < minTime.second(); i++) {
              seconds.push(i);
            }
          }
          if (
            maxTime &&
            selectedHour === maxTime.hour() &&
            selectedMinute === maxTime.minute()
          ) {
            // 在最大时间的小时和分钟内，禁用大于最大时间的秒
            for (let i = maxTime.second() + 1; i < 60; i++) {
              seconds.push(i);
            }
          }
          return seconds;
        };

        return {
          disabledHours,
          disabledMinutes,
          disabledSeconds,
        };
      };
    };

    const { type, props } = componentMap[validationType] || {
      type: InlineInput,
      props: {
        placeholder: '请输入文本',
      },
    };

    // 根据组件类型添加约束
    const componentProps = { ...props };

    if (type === DatePicker) {
      componentProps.disabledDate = getDisabledDate(faMinValue, faMaxValue);

      // 对于日期时间类型，添加时间选择和限制
      if (validationType === '日期与时间') {
        componentProps.showTime = true;
        componentProps.disabledTime = date => {
          if (!date) return {};
          const currentDate = dayjs(date);

          // 获取最小和最大时间
          const minDate = faMinValue ? dayjs(faMinValue) : null;
          const maxDate = faMaxValue ? dayjs(faMaxValue) : null;

          const disabledHours = () => {
            const hours = [];

            // 如果是最小日期，禁用之前的小时
            if (
              minDate &&
              currentDate.format('YYYY-MM-DD') === minDate.format('YYYY-MM-DD')
            ) {
              for (let i = 0; i < minDate.hour(); i++) {
                hours.push(i);
              }
            }

            // 如果是最大日期，禁用之后的小时
            if (
              maxDate &&
              currentDate.format('YYYY-MM-DD') === maxDate.format('YYYY-MM-DD')
            ) {
              for (let i = maxDate.hour() + 1; i < 24; i++) {
                hours.push(i);
              }
            }

            return hours;
          };

          const disabledMinutes = hour => {
            const minutes = [];

            // 如果是最小日期且当前小时等于最小时间的小时
            if (
              minDate &&
              currentDate.format('YYYY-MM-DD') ===
                minDate.format('YYYY-MM-DD') &&
              hour === minDate.hour()
            ) {
              for (let i = 0; i < minDate.minute(); i++) {
                minutes.push(i);
              }
            }

            // 如果是最大日期且当前小时等于最大时间的小时
            if (
              maxDate &&
              currentDate.format('YYYY-MM-DD') ===
                maxDate.format('YYYY-MM-DD') &&
              hour === maxDate.hour()
            ) {
              for (let i = maxDate.minute() + 1; i < 60; i++) {
                minutes.push(i);
              }
            }

            return minutes;
          };

          const disabledSeconds = (hour, minute) => {
            const seconds = [];

            // 如果是最小日期且当前小时分钟等于最小时间的小时分钟
            if (
              minDate &&
              currentDate.format('YYYY-MM-DD') ===
                minDate.format('YYYY-MM-DD') &&
              hour === minDate.hour() &&
              minute === minDate.minute()
            ) {
              for (let i = 0; i < minDate.second(); i++) {
                seconds.push(i);
              }
            }

            // 如果是最大日期且当前小时分钟等于最大时间的小时分钟
            if (
              maxDate &&
              currentDate.format('YYYY-MM-DD') ===
                maxDate.format('YYYY-MM-DD') &&
              hour === maxDate.hour() &&
              minute === maxDate.minute()
            ) {
              for (let i = maxDate.second() + 1; i < 60; i++) {
                seconds.push(i);
              }
            }

            return seconds;
          };

          return {
            disabledHours,
            disabledMinutes,
            disabledSeconds,
          };
        };
      }
    } else if (type === TimePicker) {
      componentProps.disabledTime = getDisabledTime(faMinValue, faMaxValue);
    }

    return h(type, {
      value: state.inputValues[key],
      'onUpdate:value': value => (state.inputValues[key] = value),
      ...(type === DatePicker || type === TimePicker
        ? {
            locale: locale,
            // 日期和时间选择器使用 onChange,选完直接存起来
            onChange: value => handleAnswerChange(key, value),
          }
        : {
            // InlineInput输入性质的组件 使用 onBlur和onChange
            onChange: e => {
              const value = e.target.value;
              let isValid = true;
              state.errorMessage = '';

              // 根据不同的验证类型进行校验
              switch (validationType) {
                case '手机':
                  if (!/^1[3-9]\d{9}$/.test(value)) {
                    isValid = false;
                    state.errorMessage =
                      '请输入正确的手机号码（如：13000000000）';
                  }
                  break;

                case '整数':
                  if (!/^-?\d+$/.test(value)) {
                    isValid = false;
                    state.errorMessage = '请输入正确的整数（如：12345678）';
                  } else {
                    const numValue = parseInt(value);
                    if (faMinValue && numValue < Number(faMinValue)) {
                      isValid = false;
                      state.errorMessage = `最小值不能小于${faMinValue}(当前是${numValue})`;
                    }
                    if (faMaxValue && numValue > Number(faMaxValue)) {
                      isValid = false;
                      state.errorMessage = `最大值不能超过${faMaxValue}(当前是${numValue})`;
                    }
                  }
                  break;

                case '小数':
                  if (!/^-?\d*\.?\d+$/.test(value)) {
                    isValid = false;
                    state.errorMessage =
                      '请输入正确的数字（如：12.34），请注意使用英文字符格式';
                  } else {
                    const numValue = parseFloat(value);
                    if (faMinValue && numValue < Number(faMinValue)) {
                      isValid = false;
                      state.errorMessage = `最小值不能小于${faMinValue}(当前是${numValue})`;
                    }
                    if (faMaxValue && numValue > Number(faMaxValue)) {
                      isValid = false;
                      state.errorMessage = `最大值不能超过${faMaxValue}(当前是${numValue})`;
                    }
                  }
                  break;

                case '文本': {
                  const textLength = value.length;
                  if (faMinValue && textLength < Number(faMinValue)) {
                    isValid = false;
                    state.errorMessage = `您的输入小于最小输入字数${faMinValue},当前字数为${textLength}`;
                  }
                  if (faMaxValue && textLength > Number(faMaxValue)) {
                    isValid = false;
                    state.errorMessage = `您的输入已超过最大字数${faMaxValue},当前字数为${textLength}`;
                  }
                  break;
                }
              }

              // 如果验证通过，清空错误信息
              if (isValid) {
                state.errorMessage = '';
              }
            },

            onBlur: e => {
              // 在失焦时，如果没有错误信息，则可以缓存答案
              if (!state.errorMessage) {
                handleAnswerChange(key, e.target.value);
              }
            },
          }),
      ...componentProps,
    });
  };

  Array.from(element.querySelectorAll('.filling-box')).forEach(inputItem => {
    const key = inputItem.getAttribute('optionNo');
    state.inputValues[key] ??= undefined;
    const container = document.createElement('span');
    container.style.display = 'inline-block';
    inputItem.parentNode.replaceChild(container, inputItem);
    render(createComponent(inputItem, key), container);
  });
};

const setTitleRef = el => {
  if (el) {
    nextTick(() => {
      transformFilling(el);
    });
  }
};

// 向父级暴露组件内部状态
defineExpose({
  state,
});
</script>

<style lang="less" scoped>
.title-wrap {
  display: flex;
  align-items: flex-start;
  font-weight: 500;
  font-size: 20px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 24px;
  .title {
    flex: 1;
  }
}
.errorMessage {
  display: flex;
  align-items: center;
  padding: 8px 4px 8px 36px;

  background-color: #ffecec;

  margin-top: 10px;
  .errorMessage_text {
    color: #262626;
    margin-left: 10px;
    font-size: 14px;
  }
}

.answer-area {
  padding-top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #f5f5f5;
  border-radius: 4px;
  font-weight: 500;
  font-size: 26px;
  color: #d7d7d7;
}
</style>
