<template>
  <span
    ref="inputRef"
    class="inline-question-blank"
    :class="{ 'has-placeholder': showPlaceholder }"
    :data-placeholder="showPlaceholder ? placeholder : ''"
    contenteditable="true"
    @input="handleInput"
    @blur="handleBlur"
    @focus="handleFocus"
    @keydown="handleKeydown"
    @paste="handlePaste"
  ></span>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入'
  }
})

const emit = defineEmits(['update:value', 'change', 'blur', 'focus'])

const inputRef = ref(null)
const isFocused = ref(false)

// 计算是否显示placeholder
const showPlaceholder = computed(() => {
  return (!props.value || props.value === '') && !isFocused.value
})

// 处理输入事件
const handleInput = (e) => {
  const value = e.target.textContent || ''
  emit('update:value', value)
  emit('change', { target: { value } })
}

// 处理失焦事件
const handleBlur = (e) => {
  isFocused.value = false
  const value = e.target.textContent || ''
  emit('blur', { target: { value } })
}

// 处理聚焦事件
const handleFocus = (e) => {
  isFocused.value = true
  emit('focus', e)
}

// 处理键盘事件
const handleKeydown = (e) => {
  // 阻止换行
  if (e.key === 'Enter') {
    e.preventDefault()
    e.target.blur()
  }
}

// 处理粘贴事件，只保留纯文本
const handlePaste = (e) => {
  e.preventDefault()
  const text = e.clipboardData?.getData('text/plain') || ''
  if (text && inputRef.value) {
    // 使用现代API替代已弃用的execCommand
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.deleteContents()
      range.insertNode(document.createTextNode(text))
      range.collapse(false)
    }
    // 触发input事件
    handleInput({ target: inputRef.value })
  }
}

// 监听value变化，更新DOM内容
watch(() => props.value, (newValue) => {
  console.log('InlineInput value changed:', newValue)
  if (inputRef.value) {
    const currentText = inputRef.value.textContent || ''
    const newText = (newValue !== undefined && newValue !== null) ? newValue : ''
    console.log('Current text:', currentText, 'New text:', newText)
    if (currentText !== newText) {
      inputRef.value.textContent = newText
      console.log('Updated textContent to:', newText)
    }
  }
}, { immediate: true })

// 监听聚焦状态变化，处理placeholder显示
watch(isFocused, (focused, oldFocused) => {
  // 只有在真正从未聚焦变为聚焦，且当前没有内容时才清空
  if (focused && oldFocused === false && !props.value) {
    nextTick(() => {
      if (inputRef.value && inputRef.value.textContent === '') {
        inputRef.value.focus()
      }
    })
  }
})
</script>

<style lang="less" scoped>
.inline-question-blank {
  display: inline;
  min-width: 56px;
  -webkit-user-modify: read-write-plaintext-only;
  background-position-y: 100%;
  background-repeat: no-repeat;
  background-size: 100% 1px;
  word-break: break-all;
  background-image: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));
  padding: 0px 4px;
  margin: 0px 4px;
  outline: none;
  position: relative;

  &:focus {
    background-image: linear-gradient(#1890ff, #1890ff);
  }

  // 使用伪元素实现placeholder效果
  &.has-placeholder:empty::before {
    content: attr(data-placeholder);
    color: #bfbfbf;
    pointer-events: none;
    user-select: none;
  }

  // 确保空内容时也有最小高度
  &:empty {
    min-height: 1em;
  }
}
</style>
