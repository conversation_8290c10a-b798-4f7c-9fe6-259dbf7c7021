<template>
  <a-modal
    v-model:open="state.open"
    width="1150px"
    :title="state.record.name"
    :maskClosable="false"
    :destroyOnClose="true"
    :confirmLoading="state.confirmLoading"
    @ok="confirm"
    :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
  >
    <div class="Model">
      <!-- <div class="dimension_box">
        <div class="dimension_box_title">对比维度：</div>
        <a-select
          ref="select"
          v-model:value="state.dimNums"
          style="width: 300px; margin-left: 8px; margin-right: 8px"
          @focus="focus"
          @change="handleChange"
        >
          <a-select-option :disabled="state.copyDimNum < 1" :value="1"
            >1</a-select-option
          >
          <a-select-option :disabled="state.copyDimNum < 2" :value="2"
            >2</a-select-option
          >
          <a-select-option :disabled="state.copyDimNum < 3" :value="3"
            >3</a-select-option
          >
          <a-select-option :disabled="state.copyDimNum < 4" :value="4"
            >4</a-select-option
          >
        </a-select>
        <div class="dimension_box_tip">
          默认为模型对比维度，若当前指标特殊，请选择其他对比维度（不支持超过当前模型维度）
        </div>
      </div> -->
      <p class="tips">请选择本题所需使用的图表</p>
      <a-radio-group class="radio_group" v-model:value="state.echartType">
        <a-radio
          :value="item.value"
          class="radio_item"
          v-for="item in CHART_MAP"
          :key="item.id"
        >
          <div
            :class="[
              'radio_item_wrap',
              { active_radio_item_wrap: state.echartType === item.value },
            ]"
          >
            <span class="name">{{ item.name }}</span>
            <img :src="item.img" alt="" />
            <!-- 配置按钮 - 右下角 -->
            <div
              class="config-btn"
              @click.stop="openChartConfig(item)"
              title="配置图表参数"
            >
              <SettingOutlined style="color: #ffffff" />
            </div>
          </div>
        </a-radio>
      </a-radio-group>

      <a-spin :spinning="state.detailsLoading">
        <div class="semantics">
          <div class="header">
            <div class="left">
              <p>语义：</p>
              <a-select
                v-model:value="state.semanticsId"
                style="width: 300px"
                placeholder="请选择"
                :options="state.semanticsList"
                :fieldNames="{ label: 'name', value: 'id' }"
                @change="updateSemantics"
              ></a-select>
            </div>
          </div>
          <a-button
            @click="openSemanticsConfig"
            type="link "
            class="set_params"
          >
            <SettingOutlined />设置语义参数</a-button
          >

          <div class="template_wrap">
            <div class="edit_area">
              <div class="edit_header">
                <a-select
                  v-model:value="state.templateId"
                  :options="state.templateOptions"
                  :fieldNames="{ label: 'name', value: 'id' }"
                  @change="updateTemplateId"
                >
                  <template #suffixIcon>
                    <div class="select_icon">
                      <i class="iconfont icon-xuanzegengduo"></i>
                    </div>
                  </template>
                </a-select>
                <div class="right" v-if="!state.editTemplate">
                  <p class="edit" @click="toggleEdit(true)">编辑语义</p>
                </div>
                <div class="right" v-else>
                  <p class="save" @click="toggleEdit(false, true)">保存编辑</p>
                  <p class="cancel" @click="toggleEdit(false)">取消</p>
                </div>
              </div>
              <div class="template_info" v-if="!state.editTemplate">
                <p class="label">标准语义</p>
                <p class="content">{{ state.templateData }}</p>
                <p class="label">内容范本</p>
                <p class="content">{{ templateSample }}</p>
              </div>
              <div class="edit_template" v-else>
                <p class="tips">
                  编辑语义，请参考数据结构及标准语义输入正确的语义格式
                </p>
                <a-textarea v-model:value="state.templateData" />
              </div>
            </div>
            <div class="code_area">
              <p class="label">数据结构</p>
              <highlightjs
                v-if="state.dataStructure"
                language="javascript"
                :code="state.dataStructure"
              />
            </div>
          </div>
        </div>
      </a-spin>

      <Semantic ref="semanticRef" @confirm="saveSemantic" />
      <ChartConfigModal ref="chartConfigModalRef" @confirm="saveChartConfig" />
      <SemanticsConfingModal
        ref="SemanticsConfingModalRef"
        @confirm="saveSemanticsConfig"
      />
    </div>
  </a-modal>
</template>

<script setup>
import * as prettier from 'https://unpkg.com/prettier@3.3.3/standalone.mjs';
import prettierPluginBabel from 'https://unpkg.com/prettier@3.3.3/plugins/babel.mjs';
import prettierPluginEstree from 'https://unpkg.com/prettier@3.3.3/plugins/estree.mjs';

import { message } from 'ant-design-vue';
import table from '@/assets/images/table.png';
import hBarChart from '@/assets/images/h-bar-chart.png';
import barChart from '@/assets/images/bar-chart.png';
import pieChart from '@/assets/images/pie-chart.png';
import radarChart from '@/assets/images/radar-chart.png';
import hStackedChart from '@/assets/images/h-stacked-chart.png';
import stackedChart from '@/assets/images/stacked-chart.png';
import lineChart from '@/assets/images/line-chart.png';
import scatterPlot from '@/assets/images/scatter-plot.png';
import columnLineChart from '@/assets/images/column-line-chart.png';
import doubleStackChart from '@/assets/images/double-stack-chart.png';
import scatterLineChart from '@/assets/images/scatter-line-chart.png';
import Semantic from './Semantic.vue';

import ChartConfigModal from './ChartConfigModal.vue';
import SemanticsConfingModal from './SemanticsConfingModal.vue';

import diffbar from '@/assets/images/diffbar.png'; // 差异柱状图
import updown from '@/assets/images/updown.png'; // 上下柱状图
import overlap from '@/assets/images/overlap.png'; // 重叠柱状图
import barLine from '@/assets/images/barLine.png'; // 组合柱状图
import bar1 from '@/assets/images/bar1.png'; // 排序条形图
import nested_hbar from '@/assets/images/nested_hbar.png'; // 双坐标轴条形图
import diverging_grouped from '@/assets/images/diverging_grouped.png'; // 双侧横向堆叠图
import barStacked from '@/assets/images/barStacked.png'; // 单侧横向堆叠图
import heat from '@/assets/images/heat.png'; // 热力图
import scatter_quad from '@/assets/images/scatter_quad.png'; // 象限散点图
const route = useRoute();

/** 图标映射 */
// const CHART_MAP = [
//   {
//     id: 1,
//     img: table,
//     name: '二维列表',
//     value: 'table',
//   },
//   {
//     id: 2,
//     img: hBarChart,
//     name: '多色条图形',
//     value: 'bar',
//   },
//   {
//     id: 3,
//     img: barChart,
//     name: '多色柱状图',
//     value: 'column',
//   },
//   {
//     id: 4,
//     img: pieChart,
//     name: '饼状图',
//     value: 'pie',
//   },
//   {
//     id: 5,
//     img: radarChart,
//     name: '雷达图',
//     value: 'radar',
//   },
//   {
//     id: 6,
//     img: hStackedChart,
//     name: '横状堆叠图',
//     value: 'barStacked',
//   },
//   {
//     id: 7,
//     img: stackedChart,
//     name: '竖状堆叠图',
//     value: 'columnStacked ',
//   },
//   {
//     id: 8,
//     img: lineChart,
//     name: '折线图',
//     value: 'line',
//   },
//   {
//     id: 9,
//     img: scatterPlot,
//     name: '散点图',
//     value: 'scatter',
//   },
//   {
//     id: 10,
//     img: columnLineChart,
//     name: '柱状-折线图',
//     value: 'barLine',
//   },
//   // {
//   //   img: doubleStackChart,
//   //   name: '双坐标堆叠图',
//   //   value: 'table'
//   // },
//   {
//     id: 11,
//     img: scatterLineChart,
//     name: '散点-折线图',
//     value: 'scatterLine',
//   },
// ];

// 用新的
const CHART_MAP = [
  {
    id: 1,
    img: lineChart,
    name: '折线图',
    value: 'line_chart',
    configuration: {},
  },
  {
    id: 2,
    img: barChart,
    name: '常规柱状图',
    value: 'column',
    configuration: {},
  },
  {
    id: 3,
    img: diffbar,
    name: '差异柱状图',
    value: 'diffbar',
    configuration: {},
  },
  {
    id: 4,
    img: updown,
    name: '上下柱状图',
    value: 'updown',
    configuration: {},
  },
  {
    id: 5,
    img: overlap,
    name: '重叠柱状图',
    value: 'overlap',
    configuration: {},
  },
  {
    id: 6,
    img: barLine,
    name: '组合柱状图',
    value: 'barLine',
    configuration: {},
  },
  {
    id: 7,
    img: bar1,
    name: '排序条形图',
    value: 'bar',
    configuration: {},
  },
  {
    id: 8,
    img: nested_hbar,
    name: '双坐标轴条形图',
    value: 'nested_hbar',
    configuration: {},
  },
  {
    id: 9,
    img: diverging_grouped,
    name: '双侧横向堆叠图',
    value: 'diverging_grouped',
    configuration: {},
  },
  {
    id: 10,
    img: barStacked,
    name: '单侧横向堆叠图',
    value: 'barStacked',
    configuration: {},
  },
  {
    id: 11,
    img: pieChart,
    name: '饼图',
    value: 'pie',
    configuration: {},
  },
  {
    id: 12,
    img: radarChart,
    name: '雷达图',
    value: 'radar',
    configuration: {},
  },
  {
    id: 13,
    img: heat,
    name: '热力图',
    value: 'heat',
    configuration: {},
  },
  {
    id: 14,
    img: scatter_quad,
    name: '象限散点图',
    value: 'scatter_quad',
    configuration: {},
  },
];

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm']);

const semanticRef = ref(null);
const chartConfigModalRef = ref(null);
const SemanticsConfingModalRef = ref(null);

const defaultState = {
  echartType: 'line_chart',
  semanticsId: '',
  copyDimNum: 3,
  semanticsList: [],
  record: {},
  // !! 测试数据
  // record: {
  //   code: 'METRIC_10080039',
  //   createBy: '超级管理员',
  //   createTime: '2024-08-30 15:32:00',
  //   datasourceId: 1,
  //   datasourceName: '',
  //   description: '文化理解素养各维度具体表现',
  //   id: 315,
  //   modelId: 42,
  //   modelMetricItemList: [],
  //   name: '学生文化理解素养具体表现',
  //   typeId: 1,
  //   typeName: '学生指标',
  //   versionCode: 'M202408-1008'
  // },
  details: {},
  detailsLoading: false,
  confirmLoading: false,
  templateId: '',
  templateOptions: [],
  editTemplate: false,
  // 模板数据
  templateData: '',
  dataStructure: '',
};

const state = reactive({
  open: false,
  semantic: new Map(),
  // 缓存编辑过的code {semanticsId:{templateId: templateData } }
  cacheTemplateData: new Map(),
  // 图表配置缓存 - 存储每个图表的配置
  chartConfigurations: new Map(), // key: chartValue, value: configuration object

  // 语义json配置缓存
  semanticsConfigurations: new Map(),
  ...deepClone(defaultState),
});

const templateSample = computed(() => {
  return state.templateOptions.find(item => item.id === state.templateId)
    ?.templateSample;
});

// *********************
// Default Function
// *********************

const getSemantics = async () => {
  state.detailsLoading = true;
  const { data } = await http.post('/admin/rpt/semantics/list', {});
  state.semanticsList = data || [];
  // 默认选中第一个
  getSemanticsDetails(data[0]?.id);
};

/** 格式化json */
const formatSampleData = async sampleData => {
  if (sampleData) {
    sampleData = await prettier.format(sampleData, {
      parser: 'json',
      semi: true,
      singleQuote: true,
      tabWidth: 2,
      plugins: [prettierPluginBabel, prettierPluginEstree],
    });
  }

  return sampleData;
};

/** 获取语义数据示例 */
const getSemanticsDataExample = async record => {
  state.dataStructure = '';
  // 是否存在语义参数
  const scriptParamList = state.semantic.has(state.semanticsId)
    ? state.semantic.get(state.semanticsId).scriptParamList
    : null;

  const params = {
    // 模板 ID
    templateId: +route.query.id,
    // 指标 ID
    metricId: state.record.id,
    // 语义脚本 ID
    semanticsScriptId: record.semanticsId,
    // 语义脚本参数ID
    scriptParamList,
  };

  const { data } = await http.post(
    '/admin/rpt/semantics/getSemanticsDataExample',
    params
  );
  state.dataStructure = await formatSampleData(JSON.stringify(data));
};

/** 缓存语义模版数据 */
const pushCacheTemplateData = (id, templateData) => {
  if (state.cacheTemplateData.has(state.semanticsId)) {
    let data = state.cacheTemplateData.get(state.semanticsId);
    data[id] = templateData;
    state.cacheTemplateData.set(state.semanticsId, data);
  } else {
    state.cacheTemplateData.set(state.semanticsId, { [id]: templateData });
  }
};

/** 获取语义数据示例 */
const getTemplateExample = async record => {
  const cache = state.cacheTemplateData.get(record.semanticsId) || {};
  state.templateData = '';
  console.log('cache[record.id]: ', cache[record.id]);

  if (cache[record.id]) {
    // 当前语义模版被修改过且已缓存
    state.templateData = cache[record.id];
  } else {
    // 是否存在语义参数
    const scriptParamList = state.semantic.has(state.semanticsId)
      ? state.semantic.get(state.semanticsId).scriptParamList
      : null;

    const params = {
      // 模板 ID
      templateId: +route.query.id,
      // 指标 ID
      metricId: state.record.id,
      // 语义脚本 ID
      semanticsScriptId: record.semanticsId,
      // 语义脚本参数
      scriptParamList,
      // 语义模板编码
      semantics: record.code,
    };

    const { data } = await http.post(
      '/admin/rpt/semantics/getTemplateExample',
      params
    );
    state.templateData = data;
    pushCacheTemplateData(record.id, data);
  }
};

const getTemplate = async record => {
  try {
    state.detailsLoading = true;
    await getSemanticsDataExample(record);
    await getTemplateExample(record);
  } finally {
    state.detailsLoading = false;
  }
};

const getSemanticsDetails = async id => {
  try {
    state.detailsLoading = true;
    const params = {
      id,
    };
    const { data } = await http.get('/admin/rpt/semantics/details', params);
    state.semanticsId = id;
    state.details = data;
    state.templateOptions = data.semanticsTemplateList;

    // 如果请求接口里面的scriptParamStr这个字段有值的话 那我就需要 把这个字段塞进语义的配置里
    if (data.scriptParamStr) {
      // 保存后端接口请求回来的配置内容
      const scriptParamObj = JSON.parse(data.scriptParamStr);
      state.semanticsConfigurations.set(state.semanticsId, {
        configuration: scriptParamObj,
        semanticsConfigId: '',
      });
    }

    // 判断
    if (data.scriptParamList?.length) {
      // let scriptParamList = data.scriptParamList;
      // if (state.semantic.has(id)) {
      //   // 采用缓存的语义
      //   scriptParamList = state.semantic.get(id).scriptParamList;
      // }
      // const params = {
      //   id: state.record.id,
      //   semanticsId: id,
      //   scriptParamList,
      // };
      // state.semantic.set(id, params);
      // openSemantic(params);
    }
    // 默认选择第一个语义模版
    if (data.semanticsTemplateList.length) {
      state.templateId = data.semanticsTemplateList[0].id;
      await getTemplate(data.semanticsTemplateList[0]);
    } else {
      state.templateId = '';
      state.templateData = '';
      state.dataStructure = '';
    }
  } finally {
    state.detailsLoading = false;
  }
};

// *********************
// Life Event Function
// *********************

// !! 测试数据
// getSemantics()

// *********************
// Service Function
// *********************

function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

const updateSemantics = id => {
  getSemanticsDetails(id);
};

// const createScript = async () => {
//   let { id, semanticsId, scriptParamList } = state.semantic.get(
//     state.semanticsId
//   );
//   scriptParamList = scriptParamList.map(item => {
//     return { ...item, value: item.value.filter(i => i.trim()) };
//   });
//   const params = {
//     templateId: +route.query.id,
//     metricId: id,
//     semanticsScriptId: semanticsId,
//     scriptParamList,
//   };

//   const { data } = await http.post(
//     '/admin/rpt/template/semantics/script/param/create',
//     params
//   );
//   return data;
// };

const saveSemantic = params => {
  // $ 语义参数有所改动，需要清空对应的cacheTemplateData，必须在updateTemplateId之前
  state.cacheTemplateData.delete(state.semanticsId);
  state.semantic.set(params.semanticsId, params);
  updateTemplateId(state.templateId);
};

const openSemantic = params => {
  semanticRef.value.showModal(params);
};

const toggleEdit = (flag, isSave = false) => {
  state.editTemplate = flag;
  if (isSave) {
    // 将编辑的数据进行保存
    const id = state.templateId;
    const semanticsId = state.semanticsId;
    pushCacheTemplateData(id, state.templateData);
  }
};

/** 更新模板id */
const updateTemplateId = value => {
  const data = state.templateOptions.find(item => item.id === value);
  getTemplate(data);
};

/** 创建语义数据 */
const createTemplate = async () => {
  const cacheTemplate = state.cacheTemplateData.get(state.semanticsId) || {};
  if (cacheTemplate[state.templateId]) {
    const scriptParamList = state.semantic.has(state.semanticsId)
      ? state.semantic.get(state.semanticsId).scriptParamList
      : null;
    const params = {
      // 模板 ID
      templateId: +route.query.id,
      // 指标 ID
      metricId: state.record.id,
      // 语义脚本 ID
      semanticsScriptId: state.semanticsId,
      semanticsData: cacheTemplate[state.templateId],
      // 语义脚本参数ID
      scriptParamList,
    };

    const { data } = await http.post(
      '/admin/rpt/template/semantics/data/create',
      params
    );
    return data;
  } else {
    return '';
  }
};

// *********************
// Chart Configuration Function
// *********************

/** 打开图表配置弹窗 */
const openChartConfig = chartItem => {
  const params = {
    templateId: +route.query.id,
    metricId: state.record.id,
    metricCode: state.record.code,
  };

  // 从缓存中获取已有配置，如果没有则使用空对象
  const cachedData = state.chartConfigurations.get(chartItem.value) || {};
  const existingConfig = cachedData.configuration || {};

  // 创建一个包含已有配置的chartItem副本
  const chartItemWithConfig = {
    ...chartItem,
    configuration: existingConfig,
  };

  chartConfigModalRef.value.showModal(chartItemWithConfig, params);
};

/** 保存图表配置 */
const saveChartConfig = configData => {
  // 保存配置内容和配置ID
  state.chartConfigurations.set(configData.chartValue, {
    configuration: configData.configuration,
    chartConfigurationId: configData.chartConfigurationId,
  });
};

const openSemanticsConfig = () => {
  const params = {
    templateId: +route.query.id,
    metricId: state.record.id,
    metricCode: state.record.code,
  };

  // 从缓存中获取已有配置，如果没有则使用空对象
  const cachedData = state.semanticsConfigurations.get(state.semanticsId) || {};
  const existingConfig = cachedData.configuration || {};

  // 创建一个包含已有配置的chartItem副本
  const semanticsItemWithConfig = {
    semanticsId: state.semanticsId, // 语义id
    configuration: existingConfig, // 语义对应的配置
  };

  SemanticsConfingModalRef.value.showModal(semanticsItemWithConfig, params);
};

const saveSemanticsConfig = configData => {
  // 保存配置内容和配置ID
  state.semanticsConfigurations.set(configData.semanticsId, {
    configuration: configData.configuration,
    semanticsConfigId: configData.semanticsConfigId,
  });
};

const confirm = async () => {
  try {
    state.confirmLoading = true;
    console.log(state.templateId);
    console.log(state.templateOptions);

    const semanticCode = state.templateOptions.find(
      item => item.id === state.templateId
    ).code;
    if (!semanticCode) {
      message.error('请选择语义');
      return;
    }

    // const copyId = JSON.parse(
    //   JSON.stringify(
    //     state.chartConfigurations.get(state.echartType)?.chartConfigurationId ||
    //       '没有值没有值没有值没有值没有值没有值没有值'
    //   )
    // );

    // let idssss = String(copyId || '');

    // console.log(idssss, '获取到');

    let insertParams = {
      chartConfigurationId: '',
      chart: state.echartType,
      // dimNum: state.dimNums,
      semanticCode,
      indicatorCode: state.record.code,
      description: state.record.description,
      // scriptId: '',
      // templateId: '',
      semanticsConfigId: '',
    };

    if (state.chartConfigurations.has(state.echartType)) {
      insertParams.chartConfigurationId = String(
        state.chartConfigurations.get(state.echartType).chartConfigurationId
      );
    }

    if (state.semanticsConfigurations.has(state.semanticsId)) {
      insertParams.semanticsConfigId = String(
        state.semanticsConfigurations.get(state.semanticsId).semanticsConfigId
      );
    }

    // if (state.semantic.has(state.semanticsId)) {
    //   // 是否需要创建语义参数
    //   const { scriptParamList } = state.semantic.get(state.semanticsId);

    //   // 数组必须有一项不为空
    //   const isNotValid = scriptParamList.some(
    //     item => !item.value.length || item.value.every(i => !i)
    //   );
    //   if (isNotValid) {
    //     message.error('请设置语义参数');
    //     return;
    //   }

    //   // 创建语义传参
    //   const scriptId = await createScript();
    //   insertParams.scriptId = scriptId;
    // }
    // 语义模版有改动
    // const templateId = await createTemplate();
    // insertParams.templateId = templateId;

    emit('confirm', insertParams);
    state.open = false;
  } finally {
    state.confirmLoading = false;
  }
};

// *********************
// DefineExpose Function
// *********************

const showModal = (record, detailsObj) => {
  // 重置数据
  Object.assign(state, deepClone(defaultState));
  state.semantic.clear();
  state.cacheTemplateData.clear();
  state.chartConfigurations.clear(); // 清空图表配置缓存

  state.semanticsConfigurations.clear(); // 清空语义配置缓存
  state.record = record;
  state.open = true;
  state.details = {};
  // state.copyDimNum = detailsObj.dimNums;
  // state.dimNums = detailsObj.dimNums;
  getSemantics();
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.Model {
  box-sizing: border-box;
  height: 580px;
  overflow-y: auto;
  margin: -24px;
  padding: 12px 24px;

  .tips {
    font-weight: 400;
    font-size: 14px;
    color: #595959;
    margin-bottom: 16px;
  }

  .radio_group {
    display: flex;
    overflow-y: auto;
    padding-bottom: 20px;
    :deep(.ant-radio-wrapper) {
      margin-right: 0;
      min-width: unset !important;
    }

    :deep(span.ant-radio + *) {
      padding: 0;
    }

    :deep(.ant-radio) {
      padding: 0;
      position: absolute;
      right: 28px;
      top: 8px;
      z-index: 1;
    }

    .radio_item_wrap {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      width: 257px;
      height: 145px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      background: #fff;
      margin-right: 20px;
      box-shadow: 5px 5px 5px #d7d7d7;
      cursor: pointer;

      .name {
        position: absolute;
        top: 5px;
        left: 12px;
        font-weight: 500;
        font-size: 12px;
        color: #333333;
      }

      img {
        width: 235px;
        height: 100px;
        margin-top: 12px;
      }

      .radio_item {
        position: absolute;
        right: 20px;
      }

      // 配置按钮样式 - 右下角
      .config-btn {
        position: absolute;
        bottom: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        background: #00b781;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 2;

        &:hover {
          background: #009966;
          transform: scale(1.2);
        }

        .iconfont {
          color: #fff;
          font-size: 12px;
        }
      }
    }

    .active_radio_item_wrap {
      position: relative;
      box-shadow: 4px 4px 0 #73deb3;
      border: 1px solid #73deb3;
    }
  }

  .semantics {
    background: #f5f5f5;
    padding: 20px 24px;
    margin-top: 20px;

    .set_params {
      padding: 0;
      cursor: pointer;
      height: auto;
      margin: 10px 0 0;
      color: #00b781;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: #000000;
    }

    .right {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
    }

    .active {
      color: #00b781;
    }

    .divide_line {
      margin: 0 12px;
    }

    .semantics_group {
      display: grid;
      grid-template-columns: 1fr;
      grid-row-gap: 16px;

      :deep(.ant-radio) {
        padding: 0;
        position: absolute;
        left: 8px;
        top: 14px;
        z-index: 1;
      }

      :deep(span.ant-radio + *) {
        padding: 0;
      }

      :deep(.ant-radio-wrapper) {
        margin-right: 0;
        span:nth-of-type(2) {
          width: 100%;
        }
      }

      .title {
        position: absolute;
        left: 32px;
        top: 11px;
        font-weight: 600;
        font-size: 14px;
        color: #262626;
      }

      .info {
        width: 100%;
        background: #ffffff;
        border-radius: 4px;
        padding: 40px 12px 14px 12px;
        color: #8c8c8c;
      }
    }

    .code {
      padding: 24px;
      width: 100%;
      background: #ffffff;
      border-radius: 4px;
      font-weight: 400;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .template_wrap {
    display: flex;
    margin-top: 10px;
    padding: 10px 0 10px 12px;
    background-color: #fff;
    .edit_area {
      display: flex;
      flex-direction: column;
      flex: 1;
      border-right: 1px solid #d9d9d9;
      padding-right: 14px;
      overflow: auto;

      :deep(.ant-select-selector) {
        border: none;
        box-shadow: none;
        border-right-width: 0;
        padding-left: 0;

        .ant-select-selection-item {
          font-weight: 600;
          font-size: 14px;
          color: #262626;
        }
      }
      .select_icon {
        margin-top: -2px;
        color: #000;
      }

      .edit_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .right {
          .edit {
            font-weight: 400;
            font-size: 12px;
            color: #00b781;
            background: #e6fff8;
            border-radius: 4px;
            padding: 5px 9px;
          }

          .save {
            font-weight: 400;
            font-size: 12px;
            color: #00b781;
            background: #ffffff;
            border-radius: 4px;
            padding: 5px 9px;
            border-radius: 4px;
            border: 1px solid #00b781;
          }
          .cancel {
            font-weight: 400;
            font-size: 12px;
            color: #8c8c8c;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            padding: 5px 12px;
            margin-left: 8px;
          }
        }
      }

      .template_info {
        height: 492px;
        overflow: auto;
        .label {
          position: relative;
          font-weight: 400;
          font-size: 12px;
          color: #262626;
          padding-left: 10px;
          margin: 12px 0 6px;
          &::after {
            position: absolute;
            left: 0;
            top: 7px;

            content: '';
            width: 4px;
            height: 4px;
            background: #00b781;
            border-radius: 4px;
          }
        }
        .content {
          font-weight: 400;
          font-size: 12px;
          color: #8c8c8c;
          line-height: 22px;
        }
      }
      .edit_template {
        flex: 1;
        .tips {
          font-weight: 400;
          font-size: 12px;
          color: #f58622;
          margin-bottom: 12px;
        }
        .ant-input {
          height: 462px;
        }
      }
    }

    .code_area {
      flex: 1;
      height: 528px;
      overflow: hidden;
      .label {
        font-weight: 600;
        font-size: 14px;
        color: #262626;
        padding-left: 12px;
        line-height: 32px;
      }
      :deep(pre) {
        height: calc(100% - 32px);
      }
      :deep(.hljs) {
        height: 100%;
      }
    }
  }
}

.dimension_box {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  .dimension_box_title {
    font-weight: 500;
    font-size: 14px;
    color: #000000;
  }
  .dimension_box_tip {
    font-weight: 400;
    font-size: 14px;
    color: #595959;
  }
}
</style>
