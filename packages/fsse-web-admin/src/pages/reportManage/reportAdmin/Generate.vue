<template>
  <a-modal
    v-model:open="state.open"
    :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
    width="1200px"
    title="生成报告"
    :maskClosable="false"
    :destroyOnClose="true"
    :closable="false"
    @cancel="cancel"
  >
    <div class="Generate">
      <a-form ref="formRef" class="form-container" :model="state.formState">
        <p class="step_tips">第一步：请先选择数据版本号</p>
        <div class="first_steps">
          <a-form-item
            class="report_one"
            label="模型类型："
            :rules="[{ required: true, message: '请输入模型类型' }]"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              disabled
              :value="state.record.modelName"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item
            label="数据版本号："
            class="report_one"
            name="versionCode"
            :rules="[{ required: true, message: '请选择数据版本号' }]"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-select
              @change="versionCodeChange"
              v-model:value="state.formState.versionCode"
              style="width: 100%"
              placeholder="请选择"
              :options="state.options"
              :fieldNames="{ label: 'name', value: 'code' }"
            ></a-select>
          </a-form-item>
        </div>

        <p class="step_tips">第二步：请为各指标参数设置报告本体与对比对象</p>

        <div>
          <a-table
            :pagination="false"
            :bordered="true"
            :dataSource="state.dataSource"
            :columns="columns"
          >
            <template #bodyCell="{ column, record }">
              <!-- 报告本体 -->
              <template v-if="column.key === 'entity'">
                <a-select
                  @focus="getReportEntities(record)"
                  @change="
                    (value, option) => entityChange(value, option, record)
                  "
                  v-model:value="record.entity"
                  style="width: 100%"
                  placeholder="请选择"
                  :options="state.reportEntities"
                  :fieldNames="{ label: 'name', value: 'id' }"
                ></a-select>
              </template>
              <!-- 对比对象 -->
              <template v-if="column.key === 'comparisonObjects'">
                <a-select
                  @focus="getReportEntities(record)"
                  @change="
                    (value, option) =>
                      comparisonObjectsChange(value, option, record)
                  "
                  mode="multiple"
                  v-model:value="record.comparisonObjects"
                  style="width: 100%"
                  placeholder="请选择"
                  :options="state.reportEntities"
                  :fieldNames="{ label: 'name', value: 'id' }"
                ></a-select>
              </template>
            </template>
          </a-table>
        </div>

        <p class="step_tips">
          第三步：请完成下列参数值的设置，如：参数year的值为2025
        </p>

        <ul class="form-grid">
          <li v-for="item in state.params" :key="item.id">
            <p class="paramsTitle ellipsis">参数：</p>
            <a-tooltip placement="bottomRight">
              <template #title>
                <span>{{ item.description }}</span>
              </template>
              <p class="params ellipsis">{{ item.name }}</p>
            </a-tooltip>
            <a-form-item
              label="值："
              :name="item.code"
              :rules="[{ required: true, message: '请输入' }]"
            >
              <a-input
                v-model:value="state.formState[item.code]"
                @input="saveFormCache"
                placeholder="请输入"
              />
            </a-form-item>
          </li>
        </ul>
      </a-form>
    </div>

    <template #footer>
      <a-button @click="cancel">取 消</a-button>
      <a-button type="primary" @click="generate">生成报告</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { createVNode } from 'vue';
import { Modal, message } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm']);

const formRef = ref(null);

// 缓存相关常量和函数
const CACHE_KEY = 'report_generate_form_cache';

// 保存表单数据到缓存
const saveFormCache = () => {
  const cacheData = {
    formState: { ...state.formState },
    dataSource: state.dataSource.map(item => ({
      ...item,
      entity: item.entity,
      comparisonObjects: item.comparisonObjects
    })),
    versionCode: state.formState.versionCode,
    modelId: state.record.modelId,
    templateId: state.record.id
  };
  localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
};

// 从缓存加载表单数据
const loadFormCache = () => {
  try {
    const cached = localStorage.getItem(CACHE_KEY);
    if (cached) {
      return JSON.parse(cached);
    }
  } catch (error) {
    console.warn('加载缓存数据失败:', error);
  }
  return null;
};

// 清除缓存
const clearFormCache = () => {
  localStorage.removeItem(CACHE_KEY);
};

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: row => `${row.index + 1}`,
  },
  {
    title: '参数名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '报告本体',
    dataIndex: 'entity',
    key: 'entity',
    width: '30%',
  },
  {
    title: '对比对象',
    dataIndex: 'comparisonObjects',
    key: 'comparisonObjects',
    width: '30%',
  },
];

const state = reactive({
  open: false,
  formState: {},
  record: {},
  reportEntities: [],
  params: [],
  options: [],
  dataSource: [],
});

// *********************
// Default Function
// *********************

const getParams = async modelId => {
  const { data } = await http.post('/admin/rpt/model/custom-parameter/list', {
    modelId,
  });
  state.params = data || [];
};

const cancel = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认取消`,
    okText: '确 定',
    cancelText: '取消',
    onCancel() {},
    onOk() {
      state.open = false;
      formRef.value?.resetFields();
    },
  });
};

const generateReport = async () => {
  const { ...rest } = state.formState;
  const { id: templateId, modelId } = state.record;
  const metrics = state.dataSource.map(item => {
    return {
      metricCode: item.code,
      entity: item.entity,
      comparisonObjects: item.comparisonObjects,
    };
  });
  const params = {
    modelId,
    templateId,
    datasource: 'final',
    metrics,
    parameters: rest,
    versionCode: state.formState.versionCode,
  };
  const res = await http.post('/admin/rpt/job/generation-report', params);
  message.success(res.message);
  // // 生成成功后清除缓存
  // clearFormCache();
  state.open = false;
  emit('confirm', res.data);
};

const generate = () => {
  formRef.value.validate().then(() => {
    generateReport();
  });
};

// 获取数据版本号
const getListOpt = () => {
  http
    .post('/admin/dc/data-version/list', {
      dataSourceCode: 'final',
    })
    .then(res => {
      state.options = res.data;
    });
};

// 获取指标
const getListTemplateMetric = async () => {
  const res = await http.get('/admin/rpt/template/listTemplateMetric', {
    id: state.record.id,
    versionCode: state.formState.versionCode,
  });

  state.dataSource = res.data.map((item) => ({
    ...item,
  }));
};

const showModal = async record => {
  state.record = record;

  // 先获取基础数据
  await getParams(record.modelId);
  await getListOpt();

  // 尝试加载缓存数据
  const cachedData = loadFormCache();

  if (cachedData &&
      cachedData.modelId === record.modelId &&
      cachedData.templateId === record.id) {
    // 使用缓存数据
    state.formState = { ...cachedData.formState };

    // 如果有缓存的版本号，先设置版本号并获取指标数据
    if (cachedData.versionCode) {
      state.formState.versionCode = cachedData.versionCode;
      await getListTemplateMetric();

      // 恢复表格中的选择值
      if (cachedData.dataSource && state.dataSource.length > 0) {
        state.dataSource = state.dataSource.map(item => {
          const cachedItem = cachedData.dataSource.find(cached => cached.code === item.code);
          if (cachedItem) {
            return {
              ...item,
              entity: cachedItem.entity,
              comparisonObjects: cachedItem.comparisonObjects
            };
          }
          return item;
        });
      }
    }
  } else {
    // 没有缓存或缓存不匹配，使用默认值
    state.formState = {};
    state.dataSource = [];
  }

  state.open = true;
};

// 获取报告本体	对比对象 数据的来源必须选择版本
const getReportEntities = async record => {

  const params = {
    datasource: 'final',
    versionCode: state.formState.versionCode,
    metricCode: record.code,
  };
  const { data } = await http.post('/admin/rpt/source/reportEntities', params);
  state.reportEntities = data || [];
};

const versionCodeChange = () => {
  getListTemplateMetric();
  // 保存缓存
  saveFormCache();
};

// 报告本体不能选择对比对象中已经存在的值
// record.address默认值不是数组 不能直接调用数组方法会报错的
const entityChange = (value, _option, record) => {
  if (record.comparisonObjects) {
    record.comparisonObjects = record.comparisonObjects.filter(
      item => item !== value
    );
  }
  // 保存缓存
  saveFormCache();
};
// 对比对象选择不能选择报告本体
// record.age是单选收集到的是字符串
// record.address默认值不是数组 不能直接调用数组方法会报错的
const comparisonObjectsChange = (value, _option, record) => {
  if (record.entity) {
    record.comparisonObjects = value.filter(item => item !== record.entity);
  }
  // 保存缓存
  saveFormCache();
};
defineExpose({ showModal });
</script>

<style lang="less" scoped>
.Generate {
  box-sizing: border-box;
  height: 580px;
  overflow-y: auto;
  margin: -24px;
  padding: 12px 24px;

  .first_steps {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 52px;
  }

  .form-container {
    width: 1152px;
  }

  .step_tips {
    font-weight: 400;
    font-size: 14px;
    color: #f58622;
    margin-bottom: 16px;
  }

  .report_one {
    :deep(.ant-form-item-control) {
      height: 56px;
    }
    .select_tips {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }

  :deep(.ant-form-item-label > label) {
    height: 20px;
    font-weight: 400;
    font-size: 14px;
    color: #000000;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 24px;

    li {
      display: flex;
      height: 56px;
      .params {
        flex: 1;
        height: 20px;
        font-weight: 400;
        font-size: 14px;
        color: #8c8c8c;
        margin-top: 4px;
        margin-right: 12px;
        cursor: pointer;
        text-align: left;
        &:hover {
          color: #00b781;
        }
      }
      .paramsTitle {
        height: 20px;
        font-weight: 400;
        font-size: 14px;
        color: #8c8c8c;
        margin-top: 4px;

        cursor: pointer;
        text-align: left;
      }
      :deep(.ant-form-item-label) {
        line-height: 32px;
      }
    }
  }
}
</style>
